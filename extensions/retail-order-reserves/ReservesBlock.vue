<template>
  <view class="reserves">
    <view class="reserves-info reserves-mix-mp">
      <image v-if="resource.img" class="reserves-info__picture" :src="resource.img" />
      <view class="reserves-info__block" :data-hasimg="!!resource.img">
        <view class="reserves-info__title">{{ resource.title }}</view>
        <view class="reserves-info__time">{{ resource.time }}</view>
        <view class="reserves-info__bottom">
          <view class="reserves-info__price">{{ resource.chargeType === 3 ? resource.point + resource.pointName + ' + ' : '' }}¥ {{ resource.price }}</view>
          <view class="reserves-info__count">x{{ resource.count }}</view>
        </view>
      </view>
    </view>
    <view class="reserves-form reserves-mix-mp">
      <view class="reserves-form__block" v-for="item of list" :key="item.name">
        <view class="reserves-form__label">{{ item.label }}</view>
        <view class="reserves-form__value">
          <!-- 头像 -->
          <image v-if="item.type === 5" :src="item.value" alt="" />
          <block v-else>
            {{ item.value }}
          </block>
        </view>
      </view>
    </view>
  </view>
</template>

<script>
export default {
  name: 'reserves-block',
  props: {
    resource: Object,
    applyInfoList: Array,
  },
  computed: {
    list() {
      return this.applyInfoList.filter((item) => !!item.value);
    },
  },
};
</script>

<style lang="scss" scoped>
.reserves {
  font-family: PingFang SC, sans-serif;
  color: #323233;
}

.reserves-mix-mp {
  margin: var(--theme-page-card-margin-top, 10px) var(--theme-page-card-margin-right, 12px)
    var(--theme-page-card-margin-bottom, 10px) var(--theme-page-card-margin-left, 12px);
  border-radius: var(--theme-radius-card, 8px);
  overflow: hidden;
  position: relative;
  background-color: #fff;
}
.reserves-info {
  display: flex;
  padding: 16px;
  height: 120px;
  box-sizing: border-box;

  &__picture {
    display: block;
    width: 80px;
    height: 80px;
    margin-top: 4px;
    margin-right: 10px;
    border-radius: 8px;
    object-fit: cover;
  }

  &__block {
    position: relative;
    display: flex;
    flex-flow: column;
    height: 88px;
    width: 100%;

    &[data-hasimg='true'] {
      width: calc(100% - 88px);
    }
  }

  &__title {
    font-size: 16px;
    font-weight: 500;
    line-height: 24px;
  }

  &__time {
    font-size: 12px;
    color: #969799;
    line-height: 16px;
    margin-top: 4px;
  }

  &__bottom {
    display: flex;
    justify-content: space-between;
    position: absolute;
    bottom: 0;
    width: 100%;
  }

  &__price {
    font-weight: 500;
    font-size: 14px;
    line-height: 20px;
  }

  &__count {
    font-size: 14px;
    line-height: 16px;
    color: #999;
  }
}

.reserves-form {
  &__block {
    position: relative;
    display: flex;
    justify-content: space-between;
    padding: 13px 12px 11px;
  }

  &__label {
    font-size: 14px;
    line-height: 20px;
    width: 86px;
    overflow: hidden;
    text-overflow: ellipsis;
    white-space: nowrap;
  }

  &__value {
    font-size: 14px;
    line-height: 20px;
    font-weight: 500;
    width: calc(100% - 96px);
    overflow: hidden;
    text-overflow: ellipsis;
    white-space: nowrap;
    text-align: right;

    & > img {
      width: 30px;
      height: 30px;
      border-radius: 50%;

      display: block;
      position: absolute;
      top: 50%;
      right: 12px;
      transform: translateY(-50%);
    }
  }
}
</style>
