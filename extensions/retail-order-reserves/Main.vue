<template>
  <reserves-block
    v-if="visible"
    :apply-info-list="resource.applyInfoList"
    :resource="resource.info"
  />
</template>
<script>
import { mapData } from '@youzan/ranta-helper-tee';

export default {
  data: () => ({
    reserves: {},
  }),
  computed: {
    visible() {
      const { activityId } = this.reserves;
      return !!activityId;
    },
    resource() {
      const { info = {}, userApplyInfoList = [] } = this.reserves.data;
      return {
        info,
        applyInfoList: userApplyInfoList,
      };
    },
  },
  created() {
    mapData(this, ['reserves']);
  },
};
</script>
