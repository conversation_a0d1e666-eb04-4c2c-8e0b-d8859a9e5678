// 参考order-list-core中连锁kdtId切换的逻辑实现
import { updateKdtId } from '@youzan/shop-core-tee';
import { getStorageSync, removeStorage, setStorage } from '@youzan/tee-api';

const mark = '[@wsc-tee-trade/retail-order-list]updateKdtId';
// 跳转页面前的逻辑，更新订单的kdtId为当前用户缓存kdtId
export const beforePageSkip = (kdtId) => {
  setStorage('order-list:updateKdtId:oldKdtId', getApp().getKdtId());

  updateKdtId(+kdtId, { force: false, mark });
};

// 回到页面渲染后，还原kdtId
export const afterPageShow = () => {
  const oldKdtId = getStorageSync('order-list:updateKdtId:oldKdtId');
  console.log('oldKdtIdoldKdtId', oldKdtId);
  if (oldKdtId) {
    removeStorage('order-list:updateKdtId:oldKdtId');
    console.log('进店, ', oldKdtId)
    updateKdtId(+oldKdtId, { force: false, mark });
  }
};
