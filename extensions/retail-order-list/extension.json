{"name": "@wsc-tee-trade/retail-order-list", "version": "0.0.0", "platform": ["weapp"], "lifecycle": ["onPageShow"], "displayName": "订单列表页面 - 零售额外逻辑", "description": "订单列表页面 - 零售额外逻辑", "extensionId": "@wsc-tee-trade/retail-order-list", "pathInBundle": "@wsc-tee-trade/retail-order-list", "widget": {"default": "Main", "provide": ["OrderListTabs", "ListItemHeader", "OrderListItem"], "consume": ["WscOrderListTabs", "WscListItemHeader", "WscOrderListItem"]}, "data": {"provide": {"orderListTitle": ["r", "w"], "isShowRetailOrderList": ["r", "w"]}, "consume": {"isDrugShop": ["r"], "isRetailShop": ["r"], "pageType": ["r"], "activityTab": ["r"], "yunDesignConfig": ["r"], "pointsName": ["r"], "query": ["r"], "topNavHeight": ["r"]}}, "process": {"invoke": ["activityTabChange", "gotoOrderSearch", "gotoOrderDetail", "<PERSON><PERSON><PERSON><PERSON>", "setActivityTab", "refreshOrderList"], "define": ["beforeSetupRetail"]}}