/**
 * 格式化时间
 * seconds < 1m : xx秒         12秒
 * seconds < 1h : xx分钟       12分钟
 * seconds > 1h : xx小时xx分钟  1小时12分钟
 *
 * 我对这个方法做了调整，实际上这是一个纯函数，因此入参不需要整个order对象
 * refer from: https://gitlab.qima-inc.com/weapp/wsc/blob/aea861709e08a204074525ecbc47f8ab11d89883/src/packages/trade/order/list-native/common/format.js#L362-375
 *
 * @param seconds {number} 秒数
 * @returns {string} 格式化后的时间
 */
const formatTime = (seconds: number) => {
  if (seconds < 60) {
    return `${seconds}秒`;
  }
  if (seconds < 60 * 60) {
    return `${(seconds / 60).toFixed()}分钟`;
  }
  const hour = Math.floor(seconds / 3600);
  const minute = ((seconds % 3600) / 60).toFixed() || '';
  return `${hour}小时${minute}${minute ? '分钟' : ''}`;
};

/**
 * 判断当前使用的个人中心模板是否是零售特有的
 * 模板一：零售特有的订单列表样式 = '0' 时为模板一
 * 模板二：和微商城一样的样式
 * @returns {boolean}
 * 
 * 为什么需要根据个人中心是否使用的是零售的模板对订单列表进行判断？
 * 因为零售特有的模板(模板一)会影响订单列表的样式，模板一的时候订单列表只有【当前订单，历史订单】
 * 虽然这个逻辑的确有点奇怪，但历史上零售就是这么处理的
 */
const getIsRetailUsercenterTemplate = () => {
  /* #ifdef weapp */
  return getApp()
    .getShopConfigData()
    .then((data) => {
      return (data || {}).retail_weapp_usercenter_template === '0';
    });
  /* #else */
  return Promise.resolve(false);
  /* #endif */
};

export { formatTime, getIsRetailUsercenterTemplate };
