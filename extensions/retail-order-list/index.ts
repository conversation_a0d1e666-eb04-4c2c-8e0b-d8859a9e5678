// Widgets
import MainWidget from './Main.vue';
import OrderListTabs from './widgets/OrderListTabs.vue';
import ListItemHeader from './widgets/ListItemHeader.vue';
import OrderListItem from './widgets/OrderListItem.vue';

// Utils
import { storeModules } from './store';
import { getIsRetailUsercenterTemplate } from './utils';

// Dependencies
import { mapCtxData } from '@ranta/store';
import { createStore } from '@youzan/wsc-tee-trade-common/lib/store';
import { getLocation } from '@youzan/tee-api';
import { afterPageShow } from './chainShoreHelper';

export const DEFAULT_TAB_LIST = [
  {
    type: 'current',
    text: '当前订单',
  },
  {
    type: 'history',
    text: '历史订单',
  },
];

export default class Extension {
  ctx: any;
  store: any;

  constructor(options) {
    this.ctx = options.ctx;
    this.store = createStore(this.ctx, storeModules);

    this.ctx.process.define('beforeSetupRetail', (payload) => {
      // 如果个人中心用的不是零售的模板二样式，则默认使用微商城原本的样式
      return getIsRetailUsercenterTemplate().then((isReTmp) => {
        this.store.setIsRetailUsercenterTemplate(isReTmp);
        if (!isReTmp) return payload;
        // 如果个人中心用的是模板二样式，则进行零售的特殊逻辑处理
        this.initLocation();
        return {
          ...payload,
          orderListTabOpt: {
            ...payload.orderListTabOpt,
            isShow: false, // 禁用默认的标签页，因为零售要自定义
            isShowRetailTabs: true,
            tabs: DEFAULT_TAB_LIST,
          },
          searchBarOpt: {
            ...payload.orderListTabOpt,
            isShow: false,
          },
        };
      });
    });
    this.ctx.data.orderListTitle = '订单中心';
    mapCtxData(this, [
      'isDrugShop',
      'isRetailShop',
      'pageType',
      'activityTab',
      'query',
      'yunDesignConfig',
      'pointsName',
      'topNavHeight',
    ]);
  }

  initLocation() {
    getLocation().then((res) => {
      this.store.setLocation(res);
    });
  }

  onPageShow() {
    afterPageShow();
  }

  static widgets = {
    Main: MainWidget,
    OrderListTabs,
    ListItemHeader,
    OrderListItem,
  };
}
