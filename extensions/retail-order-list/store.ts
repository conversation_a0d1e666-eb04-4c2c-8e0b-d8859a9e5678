interface StoreModule {
  state?: Record<string, any>;
  getters?: Record<string, Function>;
  actions?: (ctx: any) => Record<string, Function>;
}

const rootStore: StoreModule = {
  state: {
    isDrugShop: false,
    isRetailShop: false,
    pageType: '',
    activityTab: {},
    yunDesignConfig: {},
    query: {},
    pointsName: '积分',
    /** 个人中心是否是零售的模板二样式 */
    isReTmp: false,
    topNavHeight: 90 /* 这个默认值参考自 @wsc-tee-decorate/navigation-bar */,
    location: {}, // 当前定位
  },
  getters: {
    isHistory() {
      return this.activityTab.type === 'history';
    },
  },
  actions: (ctx: any) => {
    return {
      setIsRetailUsercenterTemplate(isReTmp) {
        this.isReTmp = isReTmp;
      },
      gotoOrderSearch() {
        // TODO: 这里我记得是要传参的 retail 场景
        ctx.process.invoke('gotoOrderSearch');
      },
      activityTabChange(tab) {
        ctx.process.invoke('activityTabChange', tab);
      },
      setLocation(location) {
        this.location = location;
      }
    };
  },
};

export const storeModules = [rootStore];
