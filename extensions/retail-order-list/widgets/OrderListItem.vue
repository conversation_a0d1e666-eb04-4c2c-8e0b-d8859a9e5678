<template>
  <view>
    <view v-if="isReTmp" class="order-item" @click="onItemClick">
      <order-process-info
        v-if="!isHistory && listItem.itemDetail"
        :itemDetail="listItem.itemDetail"
        :location="location"
      />
      <goods-card :goods-list="goods" :hotel="hotelDetail" :pay-price="payPrice" />
      <view v-if="showPayInfo && payLast" class="order-item__pay">
        <view>{{ payLast }}</view>
      </view>
    </view>
    <wsc-order-list-item
      v-else
      :list-index="listIndex"
      :list-item="listItem"
      :order-index="orderIndex"
    />
  </view>
</template>

<script>
// Components
import GoodsCard from '../components/GoodsCard';
import OrderProcessInfo from '../components/order-process-info/index.vue';
import Icon from '@youzan/vant-tee/dist/icon/index.vue';

// Dependencies
import { mapState } from '@ranta/store';
import get from '@youzan/utils/object/get';
import { formatTotalPrice } from '@youzan/wsc-tee-trade-common/lib/order-utils/format';

export default {
  components: {
    'van-icon': Icon,
    'goods-card': GoodsCard,
    OrderProcessInfo,
  },

  props: {
    listIndex: {
      type: Number,
      default: 0,
    },
    orderIndex: {
      type: Number,
      default: 0,
    },
    listItem: Object,
  },

  data() {
    return {
      showWaitingTime: false,
      waitingTimeText: '',
      fetchingWaitingTime: false,
      ...mapState(this, [
        'activityTab',
        'pointsName',
        'isReTmp',
        'isHistory',
        'location',
      ]),
    };
  },

  computed: {
    orderItemGoods() {
      if ((this.orderExtra || {}).isHotel) return this.orderItem.items.slice(0, 1);

      return this.orderItem.items;
    },

    goods() {
      return this.orderItemGoods;
    },

    hotelDetail() {
      // 新酒店商品内容
      return this.orderItem.hotel || {};
    },

    payPrice() {
      const price = get(this, 'orderItem.payInfo.payAmount', '');
      const points = this.orderItem.realPointPay;

      return formatTotalPrice(+price, points, this.pointsName);
    },

    payLast() {
      // refer from: https://gitlab.qima-inc.com/weapp/wsc/blob/aea861709e08a204074525ecbc47f8ab11d89883/src/packages/trade/order/list-native/common/format.js#L318
      return get(this, 'listItem.payInfo.last', '');
    },

    showPayInfo() {
      // refer from: https://gitlab.qima-inc.com/weapp/wsc/blob/aea861709e08a204074525ecbc47f8ab11d89883/src/packages/trade/order/list-native/common/format.js#L308
      return get(this, 'listItem.orderPermission.isShowTotalPrice', false);
    },
    orderItem() {
      return (this.listItem.orderItems && this.listItem.orderItems[this.orderIndex]) || {};
    },

    orderExtra() {
      return (this.orderItem || {}).orderExtra || {};
    },
  },

  methods: {
    onItemClick() {
      this.ctx.process.invoke('gotoOrderDetail', { listItem: this.listItem });
    },
  },
};
</script>

<style lang="scss" scoped>
@keyframes iconAnimation {
  from {
    transform: rotate(0deg);
  }

  to {
    transform: rotate(360deg);
  }
}

.order-item {

  &__pay {
    padding: 0 15px;
    line-height: 40px;
    display: flex;
    justify-content: flex-end;

    view {
      padding-left: 5px;
    }

    &__price {
      display: inline-block;
    }
  }
}
</style>
