<template>
  <view>
    <view v-if="isReTmp" class="list-item-header">
      <view class="list-item-header__shop">
        <text v-if="listItem.wayTag" class="list-item-header__tag">{{ listItem.wayTag }}</text>
        <text v-if="isHistory" class="list-item-header__shop-name">{{ listItem.shopName }}</text>
      </view>
      <view :class="['list-item-header__order-state', isHistory ? 'text-history' : '']">
        {{ isHistory ? listItem.orderStateStr : listItem.phaseDesc }}
        <text>
          {{ timeStr }}
        </text>
      </view>
    </view>
    <wsc-list-item-header
      v-else
      :list-index="listIndex"
      :list-item="listItem"
      :order-index="orderIndex"
    />
  </view>
</template>

<script>
// Components
import Icon from '@youzan/vant-tee/dist/icon/index.vue';
import CountDown from '@youzan/weapp-utils/lib/countdown';
// Dependencies
import { cdnImage } from '@youzan/tee-biz-util';
import { mapState } from '@ranta/store';

export default {
  name: 'list-item-header',

  components: {
    'van-icon': Icon,
  },

  props: {
    listIndex: {
      type: Number,
      default: 0,
    },
    listItem: Object,
  },

  data() {
    return {
      SHOP_ICON: cdnImage('public_files/2fb0f3c46457ab3c13098e8bccb92e5c.png'),
      ...mapState(this, ['isReTmp', 'isHistory']),
      timeStr: '',
      timeFinish: false,
    };
  },
  mounted() {
    // 如果有倒计时，开始倒计时
    const { cutTime } = this.listItem.itemDetail || {};
    if (cutTime) {
      this.countDown = new CountDown(cutTime, {
        onChange: (timeData, strData) => {
          this.timeStr = `${strData.hour}:${strData.minute}:${strData.second}`;
        },
        onEnd: () => {
          this.timeFinish = true;
        },
      });
      this.countDown.start();
    }
  },
  destroyed() {
    this.countDown && this.countDown.stop();
  },
};
</script>

<style lang="scss">
.list-item-header {
  position: relative;
  margin-top: 10px;
  font-size: 14px;
  color: #111;
  background: #fff;
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 14px 16px 14px;
  border-radius: 8px 8px 0 0;

  &::after {
    position: absolute;
    box-sizing: border-box;
    content: ' ';
    pointer-events: none;
    top: -50%;
    bottom: -50%;
    border: 0 solid #ebedf0;
    border-bottom-width: 1px;
    transform: scaleY(0.5);
    left: 16px;
    right: 16px;
  }

  &__tag {
    padding: 4px;
    line-height: 14px;
    border: 1px solid var(--general, #a72d25);
    color: var(--general, #a72d25);
    border-radius: 4px;
    margin-right: 4px;
  }

  &__shop {
    flex-grow: 1;
    white-space: nowrap;
    overflow: hidden;
    text-overflow: ellipsis;
    padding-right: 32px;

    &-name {
      position: relative;
      font-size: 16px;
      font-weight: 500;
      margin-left: 2px;
    }
  }

  &__order-state {
    white-space: nowrap;
    text-align: right;
    font-weight: 500;
    color: var(--general, #a72d25);
    font-size: 16px;
    &.text-history {
      position: relative;
      top: -1px;
      color: #323233;
      font-weight: normal;
    }
  }
}
.count-down-text {
  color: var(--general, #a72d25);
}
</style>
