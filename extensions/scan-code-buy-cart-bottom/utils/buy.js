import get from '@youzan/utils/object/get';

// 生成下单数据
export function generateBuyOrderData(goodsList = []) {
  const config = {};
  const items = [];
  const itemSources = [];
  const activities = [];

  const sellers = [
    {
      kdtId: get(goodsList, '[0].kdtId'),
      storeId: get(goodsList, '[0].storeId', 0),
    },
  ];

  goodsList.forEach((goods) => {
    let bizData;
    try {
      /* eslint-disable-next-line */
      bizData = JSON.parse(goods.extraAttribute || '{}').bizData;
    } catch (e) {
      /* eslint-disable-next-line */
      console.log(e);
    }
    const baseId = {
      kdtId: goods.kdtId,
      goodsId: goods.goodsId,
      skuId: goods.skuId,
      propertyIds: goods.propertyIds || [],
      activityId: goods.activityId,
      activityType: +goods.activityType || 0,
    };

    // 外部订单来源
    const tpps = goods?.bizExtension?.cartBizMark?.tpps;
    const item = {
      ...baseId,
      storeId: goods.storeId || 0,
      price: goods.payPrice || 0,
      num: goods.num,
      itemMessage: goods.messages || '',
      extensions: { tpps },
      isSevenDayUnconditionalReturn: goods.isSevenDayUnconditionalReturn || false,
    };

    if (goods.deliverTime) item.deliverTime = goods.deliverTime;

    // 0元抽奖活动标识
    const promotionMark = goods?.bizExtension?.cartBizMark?.promotionMark || '';
    if (promotionMark) item.promotionMark = promotionMark;

    const itemSource = {
      ...baseId,
      bizTracePointExt: bizData,
      cartCreateTime: goods.createdTime,
      cartUpdateTime: goods.updatedTime,
    };

    const activity = {
      ...baseId,
      activityAlias: goods.activityAlias,
    };

    items.push(item);
    itemSources.push(itemSource);
    activities.push(activity);
  });

  return {
    config,
    items,
    sellers,
    source: {
      itemSources,
      orderFrom: 'cart',
      orderMark: 'online_scan_buy',
    },
    ump: {
      activities,
    },
  };
}
