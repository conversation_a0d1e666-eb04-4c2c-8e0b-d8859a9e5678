<template>
  <view class="submit">
    <view class="submit-total">
      合计：
      <text class="price" :style="textColor">￥</text>
      <text class="price price-yuan" :style="textColor">
        {{ formatPrice[0] }}
      </text>
      <text v-if="formatPrice[1]" class="price" :style="textColor">.{{ formatPrice[1] }}</text>
    </view>
    <van-button
      class="submit-btn"
      custom-class="submit-btn-embed"
      round
      :disabled="disableSubmit"
      :color="themeColors.linearColor"
      @click="onSubmit"
    >
      结算{{ submitCountStr }}
    </van-button>
  </view>
</template>

<script>
import Tee from '@youzan/tee';
import Toast from '@youzan/vant-tee/dist/toast/toast';
import Button from '@youzan/vant-tee/dist/button/index.vue';
import args from '@youzan/weapp-utils/lib/args';
import money from '@youzan/weapp-utils/lib/money';
import { mapData } from '@youzan/ranta-helper-tee';
import { cacheOrder } from './utils/api';
import { generateBuyOrderData } from './utils/buy';

const ORDER_PATH = '/packages/order/index';

export default {
  components: {
    'van-button': Button,
  },

  data() {
    const goodsList = this.ctx.data.goodsList || [];
    return {
      goodsList,
      themeColors: {
        linearColor: '',
      },
    };
  },

  computed: {
    submitCountStr() {
      return `(${this.goodsList.length})`;
    },
    disableSubmit() {
      return this.goodsList.length <= 0;
    },
    total() {
      return this.goodsList.reduce((total, curr) => total + curr.payPrice * curr.num, 0);
    },
    formatPrice() {
      const priceArr = money(this.total).toYuan().split('.');
      if (priceArr[1] === '00') priceArr.splice(1, 1);
      else if (priceArr[1].charAt(1) === '0') priceArr[1] = priceArr[1].slice(0, 1);
      return priceArr;
    },
    textColor() {
      return `color: ${this.themeColors.general};`;
    },
  },

  created() {
    mapData(this, ['goodsList']);
    mapData(
      this,
      {
        themeColors: (val) => {
          console.log('themeColors1:', val);
          this.themeColors = {
            general: val.general,
            viceText: val['vice-text'],
            linearColor: `linear-gradient(to right, ${val['start-bg']}, ${val['end-bg']})`,
          };
        },
      },
      { isSetData: false }
    );
  },

  methods: {
    onSubmit() {
      if (this.loading) return;

      this.loading = true;
      const orderData = generateBuyOrderData(this.goodsList);
      this.cacheOrder({ goToBuyData: orderData });
    },
    cacheOrder({ goToBuyData, expressType }) {
      if (typeof expressType === 'number') {
        goToBuyData.delivery = {
          expressTypeChoice: +expressType,
        };
      }

      return cacheOrder(goToBuyData)
        .then(({ bookKey }) => {
          this.loading = false;
          // 存储起来 为了下单挽留使用
          Tee.navigate({
            url: args.add(ORDER_PATH, {
              orderFrom: 'cart',
              bookKey,
            }),
          });
        })
        .catch((err) => {
          this.loading = false;
          Toast(err.msg || '结算失败，请重试');
        });
    },
  },
};
</script>

<style lang="scss" scoped>
$bg-color: #fff;

.submit {
  height: 50tpx;
  line-height: 50tpx;
  background-color: $bg-color;
  position: fixed;
  left: 0;
  right: 0;
  bottom: 0;
  box-shadow: 0 -5px 10px -8px #d8d8d8;
  padding-bottom: constant(safe-area-inset-bottom);
  padding-bottom: env(safe-area-inset-bottom);

  &-total {
    float: left;
    margin-left: 10tpx;
    font-size: 12tpx;
  }

  .price {
    font-size: 12tpx;
    color: #f00;
    font-weight: 500;
  }

  .price-yuan {
    font-size: 20tpx;
  }

  &-btn {
    float: right;
    margin-right: 10tpx;
  }

  &-btn-embed {
    height: 36tpx;
    width: 96tpx;
  }
}
</style>
