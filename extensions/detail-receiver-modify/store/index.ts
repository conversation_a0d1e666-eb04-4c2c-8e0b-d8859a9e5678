import { createStore } from '@ranta/store';
import Toast from '@youzan/vant-tee/dist/toast/toast';
import { fetchAddressList, fetchDeliveryTimeBucket, updateReceiverInfo } from '../api';
import get from '@youzan/utils/object/get';
import formatDate from '@youzan/utils/date/formatDate';

function getTime(dateStr) {
  if (!dateStr) return 0;
  return new Date(dateStr.replace(/年|月|日/g, '-')).getTime();
}

function formatAppointmentTime(timeConfig, isSelfFetch = false) {
  if (!timeConfig) {
    return '';
  }
  if (isSelfFetch) {
    const { startTime, endTime } = timeConfig;
    return `${formatDate(startTime, 'MM月DD日')} ${formatDate(startTime, 'HH:mm')}-${formatDate(
      endTime,
      'HH:mm'
    )}`;
  }
  return `${getTime(timeConfig.startTime)},${getTime(timeConfig.endTime)}`;
}

const rootStore = {
  state: {
    // data响应式
    showReceiverModifyPopup: false,
    showAddressModifyPopup: false,
    showTimeModifyPopup: false,
    orderNo: '',
    service: {},
    showDeliveryTime: false,
    receiverInfo: {},
    themeCSS: '',
    order: {},

    // store 中定义的变量
    addressList: [], // 用户可选择地址列表
    disableAddressList: [], // 禁用地址列表
    selectedAddress: null, // 选中的地址
    addressChanged: false, // 地址是否改变

    hasNotNewTime: false, // 没有新的时间可选
    timeBucketConfig: {}, // 时间切片配置
    selectedTimeConfig: null, // 选中的配送时间
    timeChanged: false, // 时间是否改变
  },
  getters: {
    // 订单中的默认地址
    defaultAddress() {
      const { receiverInfo } = this;
      // 地址转为地址库数据格式
      const { receiverName, receiverTel, deliveryAddress } = receiverInfo;
      return {
        userName: receiverName,
        tel: receiverTel,
        addressDetail: deliveryAddress,
      };
    },
    // 如果没有选中地址，则使用默认地址
    address() {
      return this.selectedAddress || this.defaultAddress;
    },
    defaultTimeConfig() {
      return {
        text: this.service?.deliveryTime,
      };
    },
    timeConfig() {
      return this.selectedTimeConfig || this.defaultTimeConfig;
    },
  },
  actions: (ctx) => {
    return {
      getUserAddressList() {
        if (!this.orderNo) {
          return;
        }
        /**
         * 根据订单号获取地址列表并处理
         */
        fetchAddressList({
          orderNo: this.orderNo,
          expressType: this.order.expressType,
        })
          .then((res: any) => {
            this.addressList = res.addressList;
            this.disableAddressList = res.disableAddressList;
          })
          .catch((err) => {
            Toast((err && (err.message || err.msg || err)) || '获取地址失败');
          });
      },

      // 在地址列表选择地址
      selectAddress(address) {
        this.selectedAddress = address;
        this.addressChanged = true;
        // 当前展示的是收货人修改弹窗，则直接关闭地址选择弹窗
        if (this.showReceiverModifyPopup) {
          ctx.data.showAddressModifyPopup = false;
          this.getDeliveryTimeBucket();
          return;
        }
        // 否则，处理订单地址变化
        this.handleChangeOrderAddress(address);
      },
      // 处理地址变化逻辑
      handleChangeOrderAddress() {
        updateReceiverInfo({
          orderNo: this.orderNo,
          address: this.selectedAddress,
        })
          .then(() => {
            Toast('地址修改成功');
            this.afterSave();
          })
          .catch((err) => {
            Toast((err && (err.message || err.msg || err)) || '地址修改失败');
          });
      },
      // 处理保存修改逻辑
      handleSaveChange() {
        const isChanged = this.addressChanged || this.timeChanged;
        if (!isChanged) {
          return Toast('没有修改内容，无需保存');
        }

        const params: any = {
          orderNo: this.orderNo,
        };

        // 如果地址有变化，添加地址相关参数
        if (this.addressChanged) {
          params.address = this.selectedAddress;
        }

        // 如果时间有变化，添加时间相关参数

        if (this.timeChanged) {
          if (!this.selectedTimeConfig.text) {
            return Toast('请选择时间');
          }
          if (this.selectedTimeConfig.startTime) {
            // 将YYYY年MM月DD日 HH:mm格式转换为时间戳字符串
            params.appointmentTime = formatAppointmentTime(this.selectedTimeConfig);
          } else {
            params.appointmentTime = formatAppointmentTime(this.timeBucketConfig);
          }
        }

        updateReceiverInfo(params)
          .then(() => {
            Toast('修改成功');
            this.afterSave();
          })
          .catch((err) => {
            Toast((err && err.message) || err.msg || '修改失败');
          });
      },

      // 获取配送时间切片
      getDeliveryTimeBucket() {
        return fetchDeliveryTimeBucket({
          orderNo: this.orderNo,
          addressId: this.selectedAddress?.id || '',
        })
          .then((res: any) => {
            if (!res) {
              return null;
            }
            // 格式化为前端需要的格式
            this.timeBucketConfig = {
              ...res,
              startTime: res.startAppointment,
              endTime: res.endAppointment,
              deliveryTimeBucket: {
                startTime: res.startAppointment,
                endTime: res.endAppointment,
              },
              timeBucket: (res.timeBucketList || []).map((item) => {
                return {
                  ...item,
                  openTime: item.startTime,
                  closeTime: item.endTime,
                };
              }),
            };
            // 没有营业时间段，则是没开启或者是及时达，直接展示尽快送达文案。
            if (!res.timeBucketList) {
              this.hasNotNewTime = true;
              this.selectedTimeConfig = {
                text: '尽快送达',
              };
              return;
            }

            const deliveryStartTime = get(this.receiverInfo, 'orderAddress.deliveryStartTime');
            // 判断当前receiverInfo中的开始时间是否落后于接口返回的最早开始时间
            if (deliveryStartTime) {
              const earliestAvailableTime = this.timeBucketConfig.startTime;

              if (deliveryStartTime < earliestAvailableTime) {
                this.selectedTimeConfig = {
                  text: '',
                };
                this.timeChanged = true;
              }
            }

            return res;
          })
          .catch((err) => {
            Toast((err && err.message) || err.msg || '获取配送时间失败');
          });
      },
      selectTimeConfig(timeConfig) {
        this.selectedTimeConfig = timeConfig;
        this.timeChanged = true;
        // 当前展示的是收货人信息修改弹窗，则直接关闭时间选择弹窗
        if (this.showReceiverModifyPopup) {
          ctx.data.showTimeModifyPopup = false;
          return;
        }
        // 否则，处理订单时间变化
        this.handleChangeOrderTime(timeConfig);
      },
      handleChangeOrderTime() {
        updateReceiverInfo({
          orderNo: this.orderNo,
          // appointmentTime格式要求为时间戳字符串，需将YYYY年MM月DD日 HH:mm格式转换
          appointmentTime: formatAppointmentTime(
            this.selectedTimeConfig,
            this.order.expressType === 1
          ),
        })
          .then(() => {
            Toast('送达时间修改成功');
            this.afterSave();
          })
          .catch((err) => {
            Toast((err && err.message) || err.msg || '送达时间修改失败');
          });
      },
      afterSave() {
        // 后端为异步任务，接口成功实际订单未修改成功，增加延迟执行后续更新操作
        setTimeout(() => {
          this.addressChanged = false;
          this.timeChanged = false;
          ctx.data.showTimeModifyPopup = false;
          ctx.data.showAddressModifyPopup = false;
          ctx.data.showReceiverModifyPopup = false;
          ctx.process.invoke('afterReceiverInfoUpdated');
        }, 1500);
      },
    };
  },
};

export default function createModifyStore(ctx) {
  const store = createStore({
    state: () => ({
      ...rootStore.state,
    }),
    getters: {
      ...rootStore.getters,
    },
    actions: {
      ...rootStore.actions(ctx),
    },
  });
  return store;
}
