import { EDIT_MODE_MAP } from '../constants';
import get from '@youzan/utils/object/get';
import each from '../utils/each';

export default {
  popupBottom() {
    return this.isTabPage ? 49 : 0;
  },
  iconName() {
    return this.isCheckedAll ? 'checked' : 'circle';
  },

  wrapperStyle() {
    return `bottom: ${this.popupBottom}px; ${this.themeCSS}`;
  },

  rootClass() {
    // 小红书本地生活小程序里，不需要cart-bottom-safe样式
    return {
      root: `custom-class cart-bottom ${
        this.safeBottom && !this.isXhsLocalLife ? 'cart-bottom--safe' : ''
      }`,
    };
  },

  iconCustomClass() {
    return this.isCheckedAll ? 'icon--checked' : 'icon--unchecked';
  },

  submitBtnClass() {
    return this.totalNum ? 'pay-btn' : 'pay-btn--disabled';
  },

  totalNum() {
    // 当前选中商品数量
    return this.checkedGoodsList.length;
  },

  btnName() {
    return this.isEditMode ? '删除' : '结算';
  },

  /* promotionDetails优惠明细字段list是根据appType优惠类型进行区分，
  优惠券只会返回最优的优惠券，比如优惠券和满减活动，那么list里就两个元素。
  PS:如果用户未领取且无可领取的优惠券的时候，不返回优惠券对象
  然后根据优惠券对象的benefitId判断是否已领取最优优惠券，未领取则benefitId为null
  如果领了，则用户已领取最优的优惠券，底部显示【去结算】否则显示【领券结算】
  拆单情况下 均显示’去结算‘  因为拆单不好算哪个商品对应哪个优惠券
   */
  isReceiveBestCoupon() {
    // 值得注意的是，这里需要判断用户对于勾选的商品，是否已经领取最优的优惠券（单个订单只能用一张，不能叠加）
    const { promotionDetails = [] } = this.submitData || {};
    const { shopCart = {} } = this;
    const { showPromotionDetail: isShowPromotionDetail = false } = shopCart;

    const coupon = promotionDetails.filter((item) => item.appType === 105);
    if (coupon.length) {
      return coupon[0].benefitId || !isShowPromotionDetail;
    }
    return true;
  },

  cartBottomData() {
    const { shopCart } = this;
    const result = {
      // 选中了几种规格
      num: 0,
      // 合计优惠后价格
      totalPrice: 0,
      desc: '不含运费',
      // 共优惠
      totalDiscount: 0,
      // 商品总价
      goodsTotalPrice: 0,
      // 优惠明细数组
      promotionDetails: [],
      // 是否优惠明细--使用新算法
      isShowPromotionDetail: false,
      // 合计积分
      totalPointsPrice: 0,
    };
    // 外部改变勾选商品，此时在validGoods组件里已进行请求，这边只需要取即可
    // 获取当前优惠金额 - 改为从submitData获取
    const selectedPreferencePrice = get(this.submitData, 'selectedPreferencePrice', 0);
    const selectedDiscountFee = get(this.submitData, 'selectedDiscountFee', 0);

    result.promotionDetails = get(this.submitData, 'promotionDetails', []);
    //    如果promotionDetails为空数组 则表示该订单没有任何优惠活动
    result.isShowPromotionDetail = shopCart.showPromotionDetail;
    // ---为了兼容老版本,根据isShowPromotionDetail来判断 但是比如限购一件 用户购了两件 却不返回优惠明细的情况，此时前端应该再走老逻辑
    result.totalDiscount =
      result.isShowPromotionDetail && !!result.promotionDetails.length
        ? selectedDiscountFee
        : selectedPreferencePrice;
    // 是否显示优惠明细按钮
    this.isShowDiscountBar = !!result.promotionDetails.length && result.isShowPromotionDetail;
    each(this.checkedGoodsList, (goods) => {
      const { pointsPrice = 0, originPrice = 0, pointsOriginPrice = 0 } = goods;
      result.num += 1;
      // 餐饮商品需要加上加料的价格
      const feedList = goods.ingredientInfoList || [];
      let feedsPrice = 0;
      if (feedList.length > 0) {
        feedsPrice = feedList.reduce((pre, feedItem) => {
          return pre + feedItem.payPrice;
        }, 0);
      }
      // 改动 有优惠明细后 使用originPrice算总价 为了兼容老版本
      if (this.isShowDiscountBar) {
        result.goodsTotalPrice += (originPrice + feedsPrice + pointsOriginPrice) * goods.num;
      } else {
        result.goodsTotalPrice += (goods.payPrice + feedsPrice) * goods.num;
      }
      result.totalPointsPrice += pointsPrice * goods.num;
      // 选择不含税海淘商品
      if (goods.tariffRule === 0) result.desc = '不含运费和进口税';
    });
    // 减掉优惠价格，并防止出现负数
    result.totalPrice = Math.max(result.goodsTotalPrice - result.totalDiscount, 0);
    return result;
  },

  isEditMode() {
    return this.editMode === EDIT_MODE_MAP.EDIT;
  },

  recommendPromotionInfoList() {
    const { shopCart = {} } = this;
    const { recommendPromotionInfoList = [] } = shopCart;
    return recommendPromotionInfoList[0] || {};
  },
};
