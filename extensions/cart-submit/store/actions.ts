import Toast from '@youzan/vant-tee/dist/toast/toast';
import { errorToast } from '@youzan/tee-biz-util';
import args from '@youzan/utils/url/args';
import mapKeysToSnakeCase from '@youzan/utils/string/mapKeysToSnakeCase';
/* #ifdef web */
import { action } from '@youzan/zan-jsbridge';
/* #endif */
import Api from '../api';
import {
  separateGoodsForMixType,
  separateGoodsForLogistics,
  generateBuyOrderData,
  generateBuyGoodsList,
} from '../buy';
import { SEPARATE_BUY_TYPE } from '../constants';
/* #ifdef weapp */
import { getLaunchOptionsSync } from '@youzan/tee-api';
/* #endif */
import get from '@youzan/utils/object/get';

const DRUG_QUALITY_LIMIT = 10000;

export default function (ctx) {
  return {
    changeSubmitText(text) {
      this.customButtonText = text;
    },
    changeDiscountDetailVisible(visible) {
      this.discountDetailVisible = visible;
    },
    updatePopupBottom(height) {
      this.popupBottom = height;
    },

    handleCheckAllGoods() {
      // 全选
      ctx.event.emit('toggleCheckedAll'); // 通知有效商品列表
    },

    showPromotionPopup() {
      ctx.event.emit('reward:show', { show: true, scene: 'cart' });
    },

    handleBtnTap() {
      if (!this.totalNum || this.loading) return;
      if (this.isEditMode) {
        ctx.event.emit('deleteCartItems'); // 通知刷新列表
      } else {
        const list = this.checkedGoodsList.map((item) => ({
          ...item,
          ...mapKeysToSnakeCase(item),
        }));
        Promise.all([
          ctx.cloud.invoke('handleCartSubmitBeforeAsync', { list }),
          ctx.cloud.invoke('beforeOrderCreate'),
          /* #ifdef web */
          ctx.process.invokePipe('beforeBuyWithGoods', {
            list,
          }),
          /* #endif */
        ]).then(() => {
          this.buyWithGoods({
            list: this.checkedGoodsList,
          });
        });
      }
    },

    isDrugGoodsValid() {
      let isValid = true;
      const drugMap = {}; // spu级别药品的数量
      this.checkedGoodsList.forEach((item) => {
        const isPrescriptionDrug = item?.bizExtension?.cartBizMark?.IS_PRESCRIPTION_DRUG === '1';
        if (isPrescriptionDrug) {
          drugMap[item.goodsId] = (drugMap[item.goodsId] || 0) + item.num;
          if (drugMap[item.goodsId] > DRUG_QUALITY_LIMIT) {
            Toast(`为保障用药安全，每种处方药不得超过${DRUG_QUALITY_LIMIT}件`);
            isValid = false;
          }
        }
      });
      if (Object.keys(drugMap).length > DRUG_QUALITY_LIMIT) {
        Toast(`一笔订单不得超过${DRUG_QUALITY_LIMIT}种处方药`);
        isValid = false;
      }
      return isValid;
    },

    handleSeparateBuy({ list = [], expressType }) {
      this.buyWithGoods({
        list,
        expressType,
      });
    },

    sendBestCoupon() {
      return new Promise((resolve) => {
        try {
          const coupon = this.cartBottomData.promotionDetails.filter(
            (item) => item.appType === 105
          );
          Api.getVoucher({
            activityId: coupon[0].activityIds[0],
            bizName: 'shopping_cart',
            source: 'shopping_cart_auto_take',
          })
            .then(() => {
              resolve('已为你领取1张优惠券，下单享优惠');
            })
            .catch((response) => {
              const res = response?.data || {};
              resolve(res.msg || '领券失败');
            });
        } catch (err) {
          resolve('');
        }
      });
    },

    async buyWithGoods(
      { list = [], expressType }: { list: Array<{ goodsType: number }>; expressType?: string } = {
        list: [],
      }
    ) {
      this.loading = true;
      // 判断是否需要分开结算
      let tmp: Record<string, unknown> = separateGoodsForMixType(list, this.shopCart);
      if (tmp.hasCourse) {
        ctx.logger.log({
          et: 'click',
          ei: 'cart_settlement_click',
          en: '教育商品购物车结算',
        });
      }
      if (tmp.needSeparate) {
        this.loading = false;
        this.separateBuy = {
          show: true,
          type: tmp.hasCourse ? SEPARATE_BUY_TYPE.COURSE_MIX_TYPE : SEPARATE_BUY_TYPE.MIX_TYPE,
          data: tmp.data,
        };
        return;
      }
      tmp = separateGoodsForLogistics(list);
      if (tmp.needSeparate) {
        this.loading = false;
        this.separateBuy = {
          show: true,
          type: SEPARATE_BUY_TYPE.LOGISTICS,
          data: tmp.data,
        };
        return;
      }
      if (this.submitData.isNewHopeShop) {
        // 是否是新希望店铺
        // 新希望店铺下单，只支持本地存储的方式
        const goodsList = generateBuyGoodsList(list);
        if (goodsList.length) {
          this.separateBuy.show = false;
          this.goOrderNewShop({
            goodsList,
            expressType,
          });
        } else {
          Toast('请选择商品');
        }
      } else {
        const goToBuyData = generateBuyOrderData(list, this.presentData, this.selectedPromotions);
        // 自动领券
        if (!this.isReceiveBestCoupon) {
          const preToastDesc = await this.sendBestCoupon();
          goToBuyData.config.preToastDesc = preToastDesc;
        }
        if (goToBuyData.items.length) {
          this.separateBuy.show = false;
          this.goOrder({
            goToBuyData,
            expressType,
            hasCourse: list.some((item) => item.goodsType === 31),
          });
        } else {
          Toast('请选择商品');
        }
      }
    },

    goOrderNewShop({ goodsList, expressType }) {
      ctx.logger.log({
        et: 'click',
        ei: 'cartpage_buy',
        en: '结算购物车',
        si: ctx.data.kdtId,
      });

      const params: Record<string, unknown> = {
        type: 'goods',
        goods_list: goodsList,
      };

      if (typeof expressType === 'number') {
        params.expressTypeChoice = expressType;
      }

      this.loading = false;

      const dbid = ctx.lambdas.setDb(params);
      ctx.process.invoke('navigateFromCart', {
        link: args.add('/packages/order/index', {
          dbid,
          orderFrom: 'cart',
        }),
      });
    },

    /* #ifdef weapp */
    handlePrefetchDefaultSelfFetch(goToBuyData) {
      const items = goToBuyData.items.map((item) => ({
        goodsId: item?.goodsId,
        skuId: item?.skuId,
        num: item?.num,
      }));
      const { kdtId } = ctx.data;
      import('@youzan/wsc-tee-trade-common/lib/self-fetch/default').then(
        ({ fetchDefaultSelffetchPoint }) => {
          fetchDefaultSelffetchPoint({
            kdtId,
            items,
            firstOneFill: true,
          });
        }
      );
    },
    /* #endif */

    async goOrder({ goToBuyData, expressType, hasCourse }) {
      ctx.logger.log({
        et: 'click',
        ei: 'cartpage_buy',
        en: '结算购物车',
        si: ctx.data.kdtId,
      });

      if (typeof expressType === 'number') {
        goToBuyData.delivery = {
          expressTypeChoice: +expressType,
        };
      }
      try {
        /* #ifdef weapp */
        // 如果存在自提商品选中，则执行默认自提点预加载
        if (this.isHasSelfFetchGoodsSelected) {
          this.handlePrefetchDefaultSelfFetch(goToBuyData);
        }
        import('@youzan/wsc-tee-trade-common/lib/biz/trade-buy-prerender/set-cache').then(
          ({ setGoodsDataForCart }) => {
            setGoodsDataForCart({
              expressType,
              goodsBuyList: goToBuyData.items.map((item) => {
                return ctx.data.shopCart.items.find((vo) => vo.skuId === item.skuId);
              }),
            });
          }
        );
        /* #endif */
      } catch (err) {}

      return Api.postBookKey(goToBuyData)
        .then(async ({ bookKey }) => {
          const { id: addressId } = ctx.data.currentAddress || {};
          ctx.data.bookKey = bookKey;
          // 由于跳转会有延迟，延迟取消loading
          setTimeout(() => {
            this.loading = false;
          }, 1500);

          // eslint-disable-next-line @youzan/domain/forbid-hardcode-domain-name
          let link = `https://cashier.youzan.com/pay/wsctrade_buy?book_key=${bookKey}&showwxpaytitle=1&kdt_id=${
            ctx.data.kdtId
          }&address_id=${addressId || ''}`;

          // 白名单内商家跳webview
          if (this.isDrugShop && (await this.isInDrugWhiteList())) {
            // FIXME: 临时解决方案，跳转webview下单页
            ctx.process.invoke('navigateFromCart', { link });
            return;
          }
          if (hasCourse) {
            // eslint-disable-next-line @youzan/domain/forbid-hardcode-domain-name
            link = `https://cashier.youzan.com/pay/wscvis_buy?orderFrom=cart&book_key=${bookKey}&showwxpaytitle=1&kdt_id=${ctx.data.kdtId}`;
            ctx.process.invoke('navigateFromCart', { link });
            return;
          }
          /* #ifdef web */
          const platform = get(window, '_global.platform');
          // 有赞精选
          if (platform === 'youzanmars') {
            action.gotoWebview({
              url: link,
              page: 'web',
            });
            return;
          }
          const { isAlipayApp, isQQApp, isXhsApp, isTTApp, isSwanApp, isKsApp } = get(
            window,
            '_global.miniprogram',
            {}
          );

          if (isAlipayApp || isQQApp || isXhsApp || isTTApp || isSwanApp || isKsApp) {
            link = `/pages/web-view/index?src=${encodeURIComponent(link)}`;
          }

          // 私域直播来源购物车补充私域直播标
          const pdlive = args.get('pdlive', window.location.href);
          if (pdlive) {
            link = `${link}&pdlive=${pdlive}`;
          }

          ctx.process.invoke('navigateFromCart', { link });
          /* #endif */

          /* #ifdef weapp */
          ctx.process.invoke('navigateToTradeBuy', {
            navigateParams: {
              bookKey,
              orderFrom: 'cart',
              addressId: addressId || '',
            },
          });
          /* #endif */
        })
        .catch((err = {}) => {
          console.error(err);
          this.loading = false;
          errorToast(err, { message: '结算失败，请稍后重试' });
        });
    },

    async isInDrugWhiteList() {
      /* #ifdef web */
      return Promise.resolve(false);
      /* #endif */
      /* #ifdef weapp */
      return new Promise((resolve, _reject) => {
        const { scene } = getLaunchOptionsSync();
        // @ts-ignore
        const pages = (getCurrentPages && getCurrentPages()) || [];
        const page = pages[pages.length - 1] || { route: '' };
        const { route } = page;
        if (
          scene !== 1154 &&
          (route.indexOf('trade-buy/order/buy/index') > -1 ||
            route.indexOf('goods-v2/detail/index') > -1)
        ) {
          Api.getDrugWhiteList()
            .then((res: { data: Boolean } = { data: false }) => {
              if (res.data) {
                resolve(true);
              } else {
                resolve(false);
              }
            })
            .catch(() => {
              resolve(false);
            });
        } else {
          resolve(false);
        }
      });
      /* #endif */
    },

    handleCloseSeparateBuyPopup() {
      this.separateBuy.show = false;
    },

    // 购物车通知后端使用最佳优惠逻辑计算
    selectBestPrice() {
      const checkedList = this.checkedGoodsList.map((item) => ({
        activityId: 0,
        activityType: '0',
        cartId: item.cartId,
        goodsId: item.goodsId,
        skuId: item.skuId,
        kdtId: item.kdtId,
      }));
      Api.reselectGoodsActivity({
        items: checkedList,
      })
        .then((res) => {
          if (res) {
            ctx.event.emit('updateCartGoodsListWithActivity', {
              selectedPromotions: {
                activityId: 0,
                activityType: '0',
              },
            });
          } else {
            errorToast(res.msg || res, { message: '使用最佳优惠失败，请稍后重试' });
          }
        })
        .catch((err) => {
          errorToast(err, { message: '使用最佳优惠失败，请稍后重试' });
        });
    },

    initXhsLocalLife() {
      /* #ifdef web */
      const { isXhsApp } = window._global?.miniprogram || {};
      // 下单页从url中确定是否小红书本地生活订单
      const isXhsLocalLife = args.get('isXhsLocalLife');
      this.isXhsLocalLife = (isXhsLocalLife === 'true' || isXhsLocalLife === true) && isXhsApp;
      /* #endif */
    },
  };
}
