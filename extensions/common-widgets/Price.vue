<template>
  <view :class="{ 'price-container': !hasEstimatedPrice && needFlex }">
    <view class="price">
      <block v-if="pointsPrice">
        <text class="price--integer">{{ pointsPrice }}</text>
        <text class="price--unit">{{ pointsUnitStr }}</text>
      </block>
      <block v-if="showMoney">
        <text class="price--currency">¥</text>
        <text class="price--integer" :style="{ fontWeight: isPop ? 400 : 500 }">{{
          formatPrice[0]
        }}</text>
        <text v-if="formatPrice[1]" class="price--decimal">.{{ formatPrice[1] }}</text>
      </block>
    </view>

    <view v-if="formatOriginPrice" class="price--origin" :style="originStyle">
      ¥{{ formatOriginPrice }}
    </view>
  </view>
</template>

<script>
import money from '@youzan/weapp-utils/lib/money';

function _formatPrice(propsPrice) {
  const val = propsPrice;
  let newPrice = val.toString();
  if (newPrice.indexOf(',') === -1) {
    newPrice = money(newPrice).toYuan();
  }

  const formatPriceArr = newPrice.split('.');
  // 小数去 0
  const decimalReverse = +formatPriceArr[1].split('').reverse().join('');
  formatPriceArr[1] = decimalReverse.toString().split('').reverse().join('');

  if (!+formatPriceArr[1]) {
    formatPriceArr.splice(1, 1);
  }
  return formatPriceArr;
}

export default {
  props: {
    price: {
      type: [String, Number],
      required: true,
      default: 0,
    },
    originPrice: {
      type: [String, Number],
      default: 0,
    },
    pointsPrice: {
      type: [String, Number],
      default: 0,
    },
    pointsName: {
      type: String,
      default: '积分',
    },
    originStyle: {
      type: String,
      default: '',
    },
    themeGeneralColor: {
      type: String,
      default: '',
    },
    isPop: {
      type: Boolean,
      default: false,
    },

    needFlex: {
      type: Boolean,
      default: true,
    },
    themeCss: String,
  },
  computed: {
    formatOriginPrice() {
      const val = this.originPrice;
      if (!val) return;

      let originPriceCN = '';
      if (val > this.price) {
        originPriceCN = money(val).toYuan();
      } else {
        return '';
      }

      const formatPriceArr = originPriceCN.split('.');
      // 小数去 0
      const decimalReverse = +formatPriceArr[1].split('').reverse().join('');
      formatPriceArr[1] = decimalReverse.toString().split('').reverse().join('');

      if (!+formatPriceArr[1]) {
        formatPriceArr.splice(1, 1);
      }

      return formatPriceArr.join('.');
    },

    formatPrice() {
      const val = this.price;
      return _formatPrice(val);
    },

    pointsUnitStr() {
      return `${this.pointsName}${this.showMoney ? '+' : ''}`;
    },

    showMoney() {
      return this.price || !this.pointsPrice;
    },
  },
};
</script>

<style lang="scss" scoped>
.price-container {
  display: flex;
  align-items: baseline;
}
.price {
  font-size: 0;
  font-family: Avenir;
  font-weight: 500;
  color: #121212;
  line-height: 20px;
  height: 20px;
}
.price--unit {
  font-size: 12px;
  margin: 0 2px 0 1px;
}

.price--currency {
  font-size: 12px;
  margin-right: 2px;
}

.price--integer {
  font-size: 18px;
}

.price--decimal {
  font-size: 12px;
}

.price--origin {
  font-size: 12px;
  line-height: 16px;
  color: #969799;
  margin-left: 6px;
  font-weight: normal;
  text-decoration: line-through;
  font-family: Avenir, pingfang, sans-serif;
}
</style>
