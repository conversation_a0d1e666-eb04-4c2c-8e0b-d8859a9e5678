/**
 * 购物车异步数据拆分处理，不影响购物车核心数据展示，根据异步请求数据更新购物车列表。
 */
import { requestV2 } from '@youzan/tee-biz-request';

// 请求接口
const getCartAsyncData = (postData) => {
  return requestV2({
    method: 'post',
    path: '/wsctrade/cart/cart-async-data.json',
    data: postData,
    options: {
      rawResponse: true,
    },
  });
};

/**
 * 根据购物车列表数据获取商品的id
 * @param {array} clientCartWrap 购物车列表数据
 */
const getItemListParams = (clientCartWrap) => {
  const groupList = clientCartWrap?.goodsGroupList || [];
  return groupList
    .map((groupItem) => {
      return (groupItem.goodsList || []).map((item) => {
        const { goodsId, skuId, alias, cartId, num, comboDetail } = item;
        const format = {
          itemId: goodsId,
          skuId,
          alias,
          uniqueKey: String(cartId),
          num,
          comboDetail,
        };
        if (comboDetail) {
          format.comboDetail = comboDetail;
        }
        return format;
      });
    })
    .flat();
};

// 购物车异步数据数据格式化
const formatShopCart = (shopCart, resData = {} as any) => {
  const { securedItems = [], itemDispatchInfoList = [] } = resData;

  const asyncData = {
    securedItems: [],
    itemDispatchInfoList: [],
    estimatedPriceList: [],
  };

  // 提取有赞担保数据
  if (securedItems.length) {
    asyncData.securedItems = securedItems;
  }

  // 提取预计送达时间数据
  if (itemDispatchInfoList.length) {
    asyncData.itemDispatchInfoList = itemDispatchInfoList;
  }

  return asyncData;
};

// 提取预估到手价信息
const extractEstimatedPrice = (shopCart) => {
  const estimatedPriceList = [];
  const groupList = shopCart?.goodsGroupList || [];

  groupList.forEach((groupItem) => {
    (groupItem.goodsList || []).forEach((item) => {
      if (item.estimatedPrice !== undefined && item.estimatedPrice !== null) {
        estimatedPriceList.push({
          goodsId: item.goodsId,
          skuId: item.skuId,
          estimatedPrice: item.estimatedPrice,
        });
      }
    });
  });

  return estimatedPriceList;
};

const asyncDataHelper = {
  // 初始数据更新 - 直接赋值shopCart，不再调用init方法
  init(shopCart, ctx) {
    // 不再使用init方法，直接赋值
    ctx.data.shopCart = shopCart;
  },
  // 更新数据 - 将返回的异步数据赋值给ctx.data.cartAsyncData
  update(shopCart, ctx) {
    const { currentAddress, currentExpressType, currentLocation } = ctx.data;
    const itemInfoList = getItemListParams(shopCart);

    // 没有地址或者没有商品列表不需要请求接口
    if (!itemInfoList.length) {
      ctx.data.cartAsyncData = {};
      return Promise.resolve();
    }

    // 提取预估到手价信息
    const estimatedPriceList = extractEstimatedPrice(shopCart);

    // 所有异步请求需要的信息传到node端
    const params = {
      itemInfoList,
      currentAddress,
      currentExpressType,
      currentLocation,
    };

    return getCartAsyncData(params)
      .then((res: any) => {
        // 格式化异步数据
        const asyncData = formatShopCart(shopCart, res.data);

        // 合并预估到手价数据
        asyncData.estimatedPriceList = estimatedPriceList;

        // 将异步数据赋值给cartAsyncData
        ctx.data.cartAsyncData = asyncData;
        return asyncData;
      })
      .catch((err) => {
        console.log('err', err);
        ctx.data.cartAsyncData = {
          estimatedPriceList,
        };
        return {};
      });
  },
};

export default asyncDataHelper;
