{"extensionId": "@wsc-tee-trade/cart-page-setup", "name": "@wsc-tee-trade/cart-page-setup", "version": "1.7.1", "bundle": "<builtin>", "lifecycle": ["onPullDownRefresh", "onPageShow", "pageDestroyed"], "widget": {"default": "Skeleton"}, "asyncInit": true, "data": {"consume": {"isMultiStore": ["r"], "offlineId": ["r"], "openHideStore": ["r"], "themeColors": ["r"], "themeCSS": ["r"], "pageStyleConfig": ["r"], "currentAddress": ["r"], "currentExpressType": ["r"], "currentLocation": ["r"], "globalMarkId": ["r"]}, "provide": {"kdtId": ["r", "w"], "shopMetaInfo": ["r", "w"], "isDrugShop": ["r", "w"], "channelId": ["r"], "shopCart": ["r", "w"], "shopList": ["r", "w"], "unavailableItems": ["r", "w"], "shopTitle": ["r", "w"], "emptyCartPath": ["r"], "showEmptyCart": ["r"], "hasValidGoods": ["r"], "showShoppingCircle": ["r"], "title": ["r", "w"], "pageSize": ["r", "w"], "requestExtraParams": ["r", "w"], "bizName": ["r", "w"], "bookKey": ["r", "w"], "displayData": ["r", "w"], "orderData": ["r", "w"], "canSelectPresent": ["r", "w"], "dataLoaded": ["r"], "isAuthProtocol": ["r"], "submitData": ["r"], "isControlRecommendShow": ["r"], "hasResponseValidGoods": ["r"], "isTabPage": ["r", "w"], "themeStyle": ["r", "w"], "isHasSelfFetchGoodsSelected": ["r", "w"], "recommendPromotionInfoList": ["r", "w"], "selectedPromotions": ["r", "w"], "cartAsyncData": ["r", "w"], "isUpdatingCartGoodsList": ["r", "w"]}}, "event": {"listen": ["updateCartGoodsList", "updateCartGoodsListWithActivity", "updateCartAsyncData", "shopCartUpdated"], "emit": ["ORDER_KEEP:open", "cartGoodsListDidUpdate", "updatingCart", "onPullDownRefresh", "stopPullDownRefresh", "cartGoodsSku:hide"]}, "process": {"invoke": ["emptyCart", "clearInvalidGoods", "invoke-protocol", "deleteCartGoods", "batchDeleteCartGoods", "selectCartGoods", "cancelSelectCartGoods", "setCartGoodsNum", "reselectGoods", "setGroupId", "updateCartList", "changeGoodsSku", "showCartActivityPopup", "showExchangeModal"], "define": ["authProtocol", "navigateFromCart", "setGroupId", "updateCartList", "beforeCartClearHook"]}, "lambda": {"consume": ["getUserInfo"]}, "platform": ["web", "weapp"]}