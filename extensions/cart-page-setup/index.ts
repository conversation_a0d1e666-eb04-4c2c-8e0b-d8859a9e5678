import get from '@youzan/utils/object/get';
import mapKeysCase from '@youzan/weapp-utils/lib/map-keys-case';
import Toast from '@youzan/vant-tee/dist/toast/toast';
import { getPrefetchData } from '@youzan/tee-biz-prefetch';
import { getShopMetaInfo } from '@youzan/tee-biz-shop';
import { getCurrentKdtId } from '@youzan/shop-core-tee';
import { requestV2 } from '@youzan/tee-biz-request';
import {
  getWeappShuntConfig,
  NAVIGATE_TYPE,
  navigateToRantaPage,
  PAGE_TYPE,
} from '@youzan/wsc-tee-trade-common/lib/utils/navigate';
import { mapData, mapEvent, mapProcess } from '@youzan/ranta-helper-tee';
import { bridge, cloud, cloudEvent, useAsHook } from '@youzan/ranta-helper';
/* #ifdef web */
import navigate, { ZNB } from '@youzan/tee-biz-navigate';
import args from '@youzan/utils/url/args';
/* #endif */
import each from '@youzan/weapp-utils/lib/each';
import mapKeysToCameLCase from '@youzan/utils/string/mapKeysToCamelCase';
import Tee from '@youzan/tee';
import { setNavigationBarTitle } from '@youzan/tee-api';
import type {
  ClearCartTypeEnum,
  ToggleGoodsSkuParams,
  DeleteGoodsParams,
  ClearCartParams,
  TradeCartGoodsGroup,
  SelectAll,
  CartOnCartRefresh,
} from '@youzan-cloud/cloud-biz-types';
import monitorLogger, { loggerKey } from '@youzan/wsc-tee-trade-common/lib/utils/skynet-monitor';

import Skeleton from './Skeleton.vue';
import {
  cartParser,
  cloudData,
  isSameObject,
  formatPromotionInfoList,
  cartDataDiff,
} from './utils';
import asyncDataHelper from './asyncDataHelper';
import {
  SelectGoodsParams,
  TradeCartSetGoodsNumParams,
  ActivityBtnParams,
  BatchDeleteGoodsParams,
  DeleteGoodsParamsV1,
  GoodsListParams,
  PopupActivityInfo,
  ReselectGoodsParams,
  SelectGoodsParamsV1,
  SetGoodsNumParamsV1,
  ThemeColors,
} from './types';
import { getHasDesign } from '@youzan/goods-biz-utils/lib/simple-sku/design';
/* #ifdef weapp */
import ZanWeappLogger from '@youzan/zan-weapp-logger';
/* #endif */

const MULTI_STORE_NOT_CHECK_PAGES = [
  'packages/shop/multi-store/index/index',
  'packages/shop/multi-store/search/index',
  'pages/common/blank-page/index',
  'packages/ump/carve-coupon/index',
  /packages\/tangshi\//,
  /packages\/groupbuying\//,
  'pages/account/login/index', // 统一登录页面
];

const couponCodeMap = {
  *********: '优惠券活动已失效，无法再领取',
  *********: '优惠券库存不足，无法再领取',
  *********: '领取规则调整，该券不可再领',
  *********: '当前领取人数太多，请稍后再试',
};

function maybeInRetailPage(path = '') {
  if (typeof path !== 'string') return false;
  return ~path.indexOf('retail');
}

export function checkShop() {
  /* #ifdef weapp */
  const app = Tee.getApp();
  const { state } = app.$store as Record<string, any>;
  const page = getCurrentPages().pop();
  const { route: currentRoute } = page || {};

  if (
    MULTI_STORE_NOT_CHECK_PAGES.some((reg) => {
      return reg === currentRoute || ((reg as RegExp).test && (reg as RegExp).test(currentRoute));
    })
  ) {
    return;
  }

  if (maybeInRetailPage(currentRoute)) return; // 零售小程序不跳转
  const { isMultiStore, offlineId, openHideStore } = state.shop;
  // 多网点店铺 如果正在切换网点的过程中，则不再继续检查
  if (isMultiStore && !offlineId && !app?.__doingSwitchStore) {
    // 如果没有网点信息，应该直接跳到网点列表，选择网点
    app.__doingSwitchStore = true;
    if (openHideStore) {
      return Tee.navigate({ url: '/packages/shop/multi-store/select-poi/index/index' });
    }
    return Tee.navigate({ url: '/packages/shop/multi-store/index/index' });
  }
  /* #endif */
}

const shopInfoCache = {
  wechatSyncShoppingList: 0,
};

const refreshCartHandler = [];
/* #ifdef weapp */
refreshCartHandler.push(checkShop);
/* #endif */
let orderKeepFnHandler = null;
let isDirty = false;

function initRefreshCartHandler(fn) {
  refreshCartHandler.push(fn);
}

function initSetOrderKeepFnHandler(fn) {
  orderKeepFnHandler = fn;
}

function getPageShop() {
  // 隐藏好物圈入口
  // xiaolv：https://xiaolv.qima-inc.com/#/demand/search?show=true&ids=118114
  // const { state } = app.$store || {};
  // const shopInfo = state.shop || {};
  // const shopBase = shopInfo.base || {};
  // shopInfoCache.wechatSyncShoppingList = shopBase.wechat_sync_shopping_list;
  shopInfoCache.wechatSyncShoppingList = 0;
}

function refreshCartData() {
  setTimeout(() => {
    // 在走一遍进店
    // 且刷新购物车数据
    refreshCartHandler &&
      refreshCartHandler.forEach((fn) => {
        fn();
      });
  }, 300);
}

function setDirty() {
  isDirty = true;
}

function orderKeepHandler(data = {}) {
  orderKeepFnHandler && orderKeepFnHandler(data);
}

/* #ifdef weapp */
// 事件注册在js加载时执行，不会因为打开多次购物车从而注册多次事件，因此不需要执行销毁逻辑。
// 原销毁逻辑导致购物车事件销毁后，重新打开购物车后商品变更无法触发数据更新。
(() => {
  const app = getApp();
  const cartAppEvents = {
    'shop:info:change': getPageShop,
    'app:offlineId:change': refreshCartData,
    'component:sku:cart': setDirty,
    'trade:order:create': setDirty,
    'trade:add:cart': setDirty,
    'order:leave:stop': orderKeepHandler,
  };

  const eventsList = Object.keys(cartAppEvents);

  eventsList.forEach((key) => {
    app.on(key, cartAppEvents[key]);
  });

  return () => {
    eventsList.forEach((key) => {
      app.off(key, cartAppEvents[key]);
    });
  };
})();
/* #endif */

getPageShop();

export default class PageSetupExtension {
  ctx: any;

  // @ts-ignore
  // eslint-disable-next-line camelcase
  $_protocol: Promise<unknown>;

  tmpShopCart: unknown[];

  tmpShopList: unknown[];

  tmpUnavailableItems: unknown[];

  locationIsAlready: boolean;

  cachedCartData: any;

  /**
   * beforeCartClear
   * @desc [可中断]清空购物车前触发
   */
  @cloud('beforeCartClear', 'hook', { allowMultiple: true })
  beforeCartClear = useAsHook<(payload: ClearCartParams) => Promise<void>>();

  /**
   * beforeCartRefresh
   * @desc [可中断]获取购物车数据之前触发
   */
  @cloud('beforeCartRefresh', 'hook', { allowMultiple: true })
  beforeCartRefresh = useAsHook<() => Promise<void>>();

  @cloud('onCartRefresh', 'event', { allowMultiple: true })
  onCartRefresh = cloudEvent<CartOnCartRefresh>();

  /**
   * clearCart
   * @desc 清空购物车
   */
  @cloud('clearCart', 'method', { allowMultiple: false })
  clearCart(params: ClearCartParams) {
    if (params.clearGoodsType.includes('validGoods' as ClearCartTypeEnum)) {
      // 清空有效商品
      this.ctx.process.invokePipe('emptyCart');
    }
    if (params.clearGoodsType.includes('invalidGoods' as ClearCartTypeEnum)) {
      // 清空失效商品
      this.ctx.process.invokePipe('clearInvalidGoods');
    }
  }

  /**
   * goodsGroupList
   * @desc 商品分组列表(购物车商品按活动分组，每种活动有多个商品参与)
   */
  @cloud('goodsGroupList', 'data')
  goodsGroupList: TradeCartGoodsGroup[];

  /**
   * themeColors
   * @desc 主题色
   */
  @bridge('themeColors', 'data')
  themeColors: ThemeColors;

  /**
   * deleteGoods
   * @deprecated 从 2.0 开始
   * @desc 删除商品
   */
  @bridge('deleteGoods', 'process')
  deleteGoodsV1({ goods, isActivity }: DeleteGoodsParamsV1): Promise<boolean> {
    if (!goods) {
      Toast('参数错误');
      return;
    }
    const currentGoods = this.getCurrentGoods(goods);
    if (!currentGoods) {
      Toast('参数错误');
      return;
    }
    return this.ctx.process.invokePipe('deleteCartGoods', { goods: currentGoods, isActivity });
  }

  /**
   * deleteGoods
   * @desc 删除商品
   */
  @cloud('deleteGoods', 'method', { allowMultiple: false })
  deleteGoods(params: DeleteGoodsParams) {
    const { goodsList = [] } = params;
    const { shopCart } = this.ctx.data;
    let batchDeleteGoodsList = [];
    goodsList.forEach((params) => {
      (shopCart?.goodsGroupList || []).forEach((goodsGroupList) => {
        (goodsGroupList?.goodsList || []).forEach((goods) => {
          if (params.cartId === goods.cartId && params.goodsId === goods.goodsId) {
            batchDeleteGoodsList = [...batchDeleteGoodsList, goods];
          }
        });
      });
    });
    this.ctx.process.invokePipe('batchDeleteCartGoods', { goodsList: batchDeleteGoodsList });
  }

  /**
   * batchDeleteGoods
   * @deprecated 从 2.0 开始
   * @desc 批量删除商品
   */
  @bridge('batchDeleteGoods', 'process')
  async batchDeleteGoods({ goodsList }: BatchDeleteGoodsParams): Promise<void> {
    if (!goodsList) {
      Toast('参数错误');
      return;
    }
    const currentGoodsList = goodsList.map((item) => this.getCurrentGoods(item));
    if (currentGoodsList.every((item) => item)) {
      await this.ctx.process.invokePipe('batchDeleteCartGoods', { goodsList: currentGoodsList });
    }
  }

  /**
   * selectGoods
   * @deprecated 从 2.0 开始
   * @desc 购物车选中流程
   */
  @bridge('selectGoods', 'process')
  selectGoodsV1({
    rangeType = 'single',
    goods,
    kdtId,
    isActivity,
  }: SelectGoodsParamsV1): Promise<boolean> {
    const params: SelectGoodsParamsV1 = { rangeType, kdtId, isActivity };
    if (rangeType === 'single') {
      const currentGoods = this.getCurrentGoods(goods);
      if (!currentGoods) {
        Toast('参数错误');
        return;
      }
      params.goods = currentGoods;
    }
    return this.ctx.process.invokePipe('selectCartGoods', params);
  }

  /**
   * selectGoods
   * @desc 选择商品
   */
  @cloud('selectGoods', 'method', { allowMultiple: false })
  selectGoods(params: SelectGoodsParams) {
    if ((params as SelectAll).isAll) {
      this.ctx.process.invokePipe('selectCartGoods', { rangeType: 'all' });
    } else {
      const { goodsList = [] } = params as Record<string, any>;
      const { shopCart } = this.ctx.data;
      (shopCart?.goodsGroupList || []).forEach((goodsGroupList) => {
        (goodsGroupList?.goodsList || []).forEach((goods) => {
          goodsList.forEach((item) => {
            if (item.cartId === goods.cartId && item.goodsId === goods.goodsId) {
              const params = {
                rangeType: 'single',
                goods,
              };
              this.ctx.process.invokePipe('selectCartGoods', params);
            }
          });
        });
      });
    }
  }

  /**
   * unselectGoods
   * @desc 取消选中商品
   */
  @cloud('unselectGoods', 'method', { allowMultiple: false })
  unselectGoods(params: SelectGoodsParams) {
    if ((params as SelectAll).isAll) {
      return this.ctx.process.invokePipe('cancelSelectCartGoods', { rangeType: 'all' });
    }
    const { goodsList = [] } = params as Record<string, any>;
    const { shopCart } = this.ctx.data;
    (shopCart?.goodsGroupList || []).forEach((goodsGroupList) => {
      (goodsGroupList?.goodsList || []).forEach((goods) => {
        goodsList.forEach((item) => {
          if (item.cartId === goods.cartId && item.goodsId === goods.goodsId) {
            const params = {
              rangeType: 'single',
              goods,
            };
            this.ctx.process.invokePipe('cancelSelectCartGoods', params);
          }
        });
      });
    });
  }

  /**
   * cancelSelectGoods
   * @deprecated 从 2.0 开始
   * @desc 购物车取消选中流程
   */
  @bridge('cancelSelectGoods', 'process')
  cancelSelectGoods({
    rangeType = 'single',
    goods,
    kdtId,
    isActivity,
  }: SelectGoodsParamsV1): Promise<boolean> {
    const params: SelectGoodsParamsV1 = { rangeType, kdtId, isActivity };
    if (rangeType === 'single') {
      const currentGoods = this.getCurrentGoods(goods);
      if (!currentGoods) {
        Toast('参数错误');
        return;
      }
      params.goods = currentGoods;
    }
    return this.ctx.process.invokePipe('cancelSelectCartGoods', params);
  }

  /**
   * selectAllGoods
   * @deprecated 从 2.0 开始
   * @desc 购物车全选流程
   */
  @bridge('selectAllGoods', 'process')
  selectAllGoods(): Promise<boolean> {
    const params: SelectGoodsParamsV1 = { rangeType: 'all' };
    return this.ctx.process.invokePipe('selectCartGoods', params);
  }

  /**
   * cancelSelectAllGoods
   * @deprecated 从 2.0 开始
   * @desc 购物车取消全选流程
   */
  @bridge('cancelSelectAllGoods', 'process')
  cancelSelectAllGoods(): Promise<boolean> {
    const params: SelectGoodsParamsV1 = { rangeType: 'all' };
    return this.ctx.process.invokePipe('cancelSelectCartGoods', params);
  }

  /**
   * setGoodsNum
   * @deprecated 从 2.0 开始
   * @desc 商品数量设置流程
   */
  @bridge('setGoodsNum', 'process')
  setGoodsNumV1({ val, goods }: SetGoodsNumParamsV1): Promise<void> {
    if (val && goods) {
      const currentGoods = this.getCurrentGoods(goods);
      if (!currentGoods) {
        Toast('参数错误');
        return;
      }
      this.ctx.process.invokePipe('setCartGoodsNum', { val, goods: currentGoods });
    } else {
      Toast('参数错误');
    }
  }

  /**
   * setGoodsNum
   * @desc 设置商品购买数量
   */
  @cloud('setGoodsNum', 'method', { allowMultiple: false })
  setGoodsNum(params: TradeCartSetGoodsNumParams) {
    const { shopCart } = this.ctx.data;
    (shopCart?.goodsGroupList || []).forEach((goodsGroupList) => {
      (goodsGroupList?.goodsList || []).forEach((goods) => {
        if (params.cartId === goods.cartId && params.goodsId === goods.goodsId) {
          this.ctx.process.invokePipe('setCartGoodsNum', { val: params.num, goods });
        }
      });
    });
  }

  /**
   * toggleGoodsSku
   * @desc 展示商品规格弹窗（用于改变商品SKU）
   */
  @cloud('toggleGoodsSku', 'method', { allowMultiple: true })
  toggleGoodsSku(params: ToggleGoodsSkuParams) {
    if (params.isShow) {
      const { shopCart } = this.ctx.data;
      (shopCart?.goodsGroupList || []).forEach((goodsGroupList) => {
        (goodsGroupList?.goodsList || []).forEach((goods) => {
          if (params.cartId === goods.cartId && params.goodsId === goods.goodsId) {
            this.ctx.process.invokePipe('changeGoodsSku', goods);
          }
        });
      });
    } else {
      this.ctx.event.emit('cartGoodsSku:hide');
    }
  }

  /**
   * reselectGoods
   * @deprecated 从 2.0 开始
   * @desc 切换商品sku
   */
  @bridge('reselectGoods', 'process')
  reselectGoods({ goods }: ReselectGoodsParams): Promise<void> {
    return new Promise((resolve, reject) => {
      if (goods) {
        const currentGoods = this.getCurrentGoods(goods);
        if (!currentGoods) {
          reject('参数错误');
          return;
        }
        this.ctx.process
          .invokePipe('reselectGoods', { ...currentGoods, ...goods })
          .then(() => {
            resolve();
          })
          .catch((e) => {
            reject(e);
          });
      } else {
        Toast('参数错误');
        reject('参数错误');
      }
    });
  }

  /**
   * setGroupId
   * @deprecated 从 2.0 开始
   * @desc 设置购物车分组id,刷新购物车
   */
  @bridge('setGroupId', 'process')
  async setGroupId(groupId: number): Promise<void> {
    if (groupId) {
      this.ctx.process.invokePipe('setGroupId', groupId);
    }
    Toast('参数错误');
  }

  /**
   * refreshCart
   * @desc 刷新购物车（即重新获取购物车数据）
   */
  @bridge('updateCartList', 'process')
  @cloud('refreshCart', 'method', { allowMultiple: true })
  refreshCart(): Promise<void> {
    return this.ctx.process.invokePipe('updateCartList');
  }

  /**
   * changeGoodsSku
   * @deprecated 从 2.0 开始
   * @desc 设置商品sku
   */
  @bridge('changeGoodsSku', 'process')
  changeGoodsSku(goods: GoodsListParams): Promise<unknown> {
    return this.ctx.process.invokePipe('changeGoodsSku', goods);
  }

  /**
   * showPopup
   * @deprecated 从 2.0 开始
   * @desc 设置是否唤起购物车活动弹窗
   */
  @bridge('showPopup', 'process')
  showPopup(activityInfo: PopupActivityInfo) {
    this.ctx.process.invoke('showCartActivityPopup', activityInfo);
  }

  /**
   * showExchangeModal
   * @deprecated 从 2.0 开始
   * @desc 设置是否唤起活动弹窗和活动页面跳转
   */
  @bridge('showExchangeModal', 'process')
  async showExchangeModal({ activityInfo, goodsList }: ActivityBtnParams) {
    if (activityInfo) {
      const { activityType } = activityInfo;
      const ACTIVITY_TYPE_ALIAS_MAP = {
        104: 'packageBuy',
        101: 'meetReduce',
        24: 'plusBuy',
        115: 'secondHalf', // 第二支半价
      };
      activityInfo.activityTypeAlias = ACTIVITY_TYPE_ALIAS_MAP[activityType];
      this.ctx.process.invoke('showExchangeModal', { activityInfo, goodsList });
    }
  }

  /**
   * updatePageTitle
   * @deprecated 从 2.0 开始
   * @desc 更新页面标题
   * @param {string} title 页面标题
   */
  @bridge('updatePageTitle', 'process')
  updatePageTitle(title: string) {
    setNavigationBarTitle(title);
  }

  /**
   * onGoodsListChange
   * @deprecated 从 2.0 开始
   * @desc 购物车数据更改时
   */
  @bridge('onGoodsListChange', 'asyncEvent')
  onGoodsListChange = useAsHook<() => Promise<void>>();

  constructor(options) {
    this.ctx = options.ctx;
    this.ctx.data.isUpdatingCartGoodsList = false;

    monitorLogger.start({ name: loggerKey.cart_page, timeout: 4 });

    /* #ifdef web */
    // @ts-ignore
    const cartList = cartParser(window._global?.cart_list || window._global?.cartList || []);
    this.ctx.data.shopList = cartList?.shopList || [];

    const isLiveHalfPage = args.get('isLiveHalfPage', window.location.href);
    if (isLiveHalfPage) {
      // 监听直播间通知iframe购物车的消息
      window.addEventListener('message', (event) => {
        // 私域直播要支持商家独立域名，校验消息来源的成本比较大，这里消息影响面较小，暂时不校验
        if (typeof event.data === 'object') {
          const { action = '' } = event.data;
          if (action === 'refreshCart') {
            this.getCartGoodsList();
          }
        }
      });
    }
    /* #endif */

    /* #ifdef weapp */
    /**
     * 做一层兜底，防止在切流过程中有其他一方直接跳转到新版购物车
     */
    navigateToRantaPage({
      type: NAVIGATE_TYPE.REDIRECT,
      pageType: PAGE_TYPE.CART,
      fromRantaPage: true,
    });
    getApp()
      .isSwitchTab()
      .then((isTabPage) => {
        this.ctx.data.isTabPage = isTabPage;
      });
    /* #endif */

    getShopMetaInfo(getCurrentKdtId()).then((shopMetaInfo) => {
      this.ctx.data.shopMetaInfo = shopMetaInfo;
    });
    this.ctx.env.getQueryAsync().then(async (query = {}) => {
      const {
        back = '',
        channelId = 0,
        repurchaseCouponStatus,
        couponValue,
        couponUnit,
        prefetchKey,
      } = mapKeysCase.toCamelCase(query);

      this.ctx.data.emptyCartPath = back;
      this.ctx.data.showEmptyCart = false;
      this.ctx.data.channelId = channelId || 0;
      // this.ctx.data.kdtId = this.ctx.data.kdtId;
      this.ctx.data.showShoppingCircle = shopInfoCache.wechatSyncShoppingList;
      this.ctx.data.repurchaseCouponStatus = repurchaseCouponStatus;
      this.ctx.data.couponValue = couponValue;
      this.ctx.data.couponUnit = couponUnit;
      this.ctx.data.hasValidGoods = true;
      this.ctx.data.submitData = {
        selectedPreferencePrice: 0,
        isNewHopeShop: false,
      };

      // recommend
      // this.ctx.data.title = '自定义标题'
      // this.ctx.data.pageSize = 10
      this.ctx.data.bizName = 'cart';
      this.ctx.data.requestExtraParams = 'note,coupon';

      // 下单挽留相关
      this.ctx.data.bookKey = 0;
      this.ctx.data.displayData = {};
      this.ctx.data.orderData = {};

      // 是否允许控制推荐商品渲染
      this.ctx.data.isControlRecommendShow = true;
      this.ctx.data.hasResponseValidGoods = true;
      this.ctx.data.themeStyle = this.getThemeStyle();
      const userInfo = (await this.ctx.lambdas?.getUserInfo?.()) || {};
      const { protocol } = userInfo.state || {};
      this.ctx.data.isAuthProtocol = protocol;
      this.getCartGoodsList({ isPageFirstFetch: true, prefetchKey });
      this.queryShopIsDrug();
      // 把刷新购物车数据传入app.js中，监听到多网点切换时，刷新购物车数据
      initRefreshCartHandler(this.getCartGoodsList.bind(this));
      initSetOrderKeepFnHandler(this.setOrderKeepData.bind(this));
      this.initEvents();
      this.initProcess();
      this.initDataWatch();
      // 获取满减赠送是否支持自选
      this.getPresentCanSelect();
      this.toastStatus();
    });
    this.initCloudData();
  }

  static widgets = {
    Main: Skeleton,
  };

  getGoodsItemIdMap() {
    const { shopList, unavailableItems } = this.ctx.data;
    const goodsItemIdMap = {};
    each(shopList, (shop) => {
      goodsItemIdMap[shop.kdtId] = {};
      each(shop.goodsGroupList, (goodsGroup) => {
        each(goodsGroup.goodsList, (goods) => {
          // TODO 新增了 cart_id 字段，使用 cart_id 就可以判断唯一性，为了兼容有赞云，保留了 goodsId skuId等判断
          goodsItemIdMap[shop.kdtId][goods.cartId] = goods;
        });
      });
    });

    // 失效商品也可以删除
    each(unavailableItems, (item) => {
      goodsItemIdMap[item.kdtId][item.cartId] = item;
    });

    return goodsItemIdMap;
  }

  getUniqueGoodsMap() {
    const { shopList, unavailableItems } = this.ctx.data;
    const uniqueGoodsMap = {};
    each(shopList, (shop) => {
      uniqueGoodsMap[shop.kdtId] = {};
      each(shop.items, (item) => {
        uniqueGoodsMap[item.kdtId][`${item.goodsId}_${item.skuId}_${item.activityId || 0}`] = item;
      });
    });

    // 失效商品也可以删除
    each(unavailableItems, (item) => {
      uniqueGoodsMap[item.kdtId][`${item.goodsId}_${item.skuId}_${item.activityId || 0}`] = item;
    });
    return uniqueGoodsMap;
  }

  getCurrentGoods(goods) {
    goods = mapKeysToCameLCase(goods);
    const goodsItemIdMap = this.getGoodsItemIdMap();
    const uniqueGoodsMap = this.getUniqueGoodsMap();
    // cart_id 为新增字段，兼容 有赞云 调用
    if (goods.cartId) {
      return goodsItemIdMap[goods.kdtId][goods.cartId];
    }

    return uniqueGoodsMap[goods.kdtId][`${goods.goodsId}_${goods.skuId}_${goods.activityId || 0}`];
  }

  getProtocol() {
    if (!this.$_protocol) {
      this.$_protocol = Promise.resolve(this.ctx.process.invoke('invoke-protocol').pop());
    }
    return this.$_protocol;
  }

  authProtocol() {
    return new Promise((resolve) => {
      // 唤起协议弹窗
      /* #ifdef web */
      this.ctx.data.isAuthProtocol = true;
      this.ctx.data.shopList = this.tmpShopList || [];
      this.setCartGoodsData({
        shopCart: this.tmpShopCart || [],
        unavailableItems: this.tmpUnavailableItems || [],
      });
      this.tmpShopList = [];
      this.tmpShopCart = [];
      this.tmpUnavailableItems = [];
      resolve(true);
      /* #endif */
      /* #ifdef weapp */
      this.getProtocol()
        .then((protocol: Record<string, any>) => {
          return protocol.auth().then(() => {
            // 授权成功
            this.ctx.data.isAuthProtocol = true;
            // 这个值控制三方的goodsList，只有授权成功才告知三方未失效商品列表更新
            this.ctx.data.shopList = this.tmpShopList || [];
            // 通过事件告知三方未失效商品列表更新
            this.ctx.cloud.invoke('onGoodsListChange');
            this.setCartGoodsData({
              shopCart: this.tmpShopCart || [],
              unavailableItems: this.tmpUnavailableItems || [],
            });
            this.tmpShopList = [];
            this.tmpShopCart = [];
            this.tmpUnavailableItems = [];
            resolve(true);
          });
        })
        .catch(() => {
          this.ctx.data.isAuthProtocol = false;
          resolve(false);
        });
      /* #endif */
    });
  }

  onPullDownRefresh() {
    this.ctx.event.emit('onPullDownRefresh');
    this.getCartGoodsList({ isPull: true });
  }

  onPageShow() {
    mapData(this, {
      pageStyleConfig: (value) => {
        const { pageBgColor = '#f7f8fa' } = value || {};
        /* #ifdef web */
        document.body.style.backgroundColor = pageBgColor;
        /* #endif */

        /* #ifdef weapp */
        getApp().trigger('trade-cart-page-bg-color', pageBgColor);
        /* #endif */
      },
    });
    if (isDirty) {
      this.getCartGoodsList({ isPull: true });
      this.ctx.event.emit('onPullDownRefresh');
    }
    // isDirty && this.getCartGoodsList();
    isDirty = false;
    getPageShop();
  }

  queryShopIsDrug() {
    return requestV2({
      path: '/wsctrade/cartQueryShopIsDrug.json',
      method: 'GET',
    }).then((res: { valid: boolean }) => {
      const { valid = false } = res;
      this.ctx.data.isDrugShop = valid;
      if (valid) {
        setNavigationBarTitle('需求清单');
      }
    });
  }

  setCartGoodsData({ shopCart = {} as Record<string, any>, unavailableItems = [] } = {}) {
    Object.assign(this.ctx.data, {
      unavailableItems,
    });
    // 是否存在有效商品
    this.ctx.data.hasValidGoods = (shopCart.goodsGroupList || []).some(
      (goodsGroup) => !!goodsGroup.goodsList.length
    );

    // 是否展示空购物车提示, 无效、有效商品均无
    this.ctx.data.showEmptyCart = !this.ctx.data.hasValidGoods && !unavailableItems.length;

    // 直接赋值shopCart，不再调用asyncDataHelper.init方法
    this.ctx.data.shopCart = shopCart;
    // 每次购物车更新，都需要更新一次异步数据
    asyncDataHelper.update(shopCart, this.ctx);

    /* #ifdef weapp */
    // TODO: 增加排查购物车不展示商品的jira
    ZanWeappLogger.log({
      appName: 'wsc-h5-trade',
      logIndex: 'jserror_log',
      level: 'info',
      name: '购物车商品数据',
      extra: {
        isAuthProtocol: this.ctx.data.isAuthProtocol,
        kdtId: this.ctx.data.kdtId,
        buyerId: getApp().getBuyerId(),
      },
      message: JSON.stringify([
        this.ctx.data.hasValidGoods,
        (shopCart?.goodsGroupList || []).length,
        unavailableItems.length,
      ]),
    });
    /* #endif */

    this.handleDefaultSelfFetchPrefetch(shopCart);
  }

  // 默认自提点预加载-注册定位
  // 用户在购物车操作，有且仅当第一次选中了可自提商品，才会触发定位获取
  // 定位加载一次locationIsAlready则为true，再次执行将不会再次加载
  async handleDefaultSelfFetchPrefetch(shopCart) {
    if (this.locationIsAlready || !shopCart?.goodsGroupList?.length) {
      return;
    }
    const isHasSelfFetchGoodsSelected = shopCart.goodsGroupList.some((item) => {
      return item.goodsList.find(
        (goodItem) => goodItem.checked && (goodItem.logisticsTypeList || []).includes('SELF_TAKE')
      );
    });
    this.ctx.data.isHasSelfFetchGoodsSelected = isHasSelfFetchGoodsSelected;
    /* #ifdef weapp */
    if (isHasSelfFetchGoodsSelected) {
      import('@youzan/wsc-tee-trade-common/lib/self-fetch/default').then(({ registerLocation }) => {
        this.locationIsAlready = true;
        registerLocation();
      });
    }
    /* #endif */
  }

  // 缓存购物车原始数据
  cacheCartData(data: any) {
    this.cachedCartData = JSON.parse(JSON.stringify(data));
  }

  // 获取缓存的购物车数据
  getCachedCartData() {
    return this.cachedCartData;
  }

  getCartGoodsList(
    {
      isPageFirstFetch = false,
      prefetchKey,
      isPull = false,
      noToast = false,
      scene,
      markId,
    } = {} as Record<string, any>
  ) {
    return this.ctx.cloud.invoke('beforeCartRefresh').then(() => {
      this.ctx.event.emit('updatingCart');

      if (!isPull && !noToast) {
        Toast.loading({
          message: '加载中...',
        });
      }

      const data = {
        store_id: this.ctx.data.offlineId || 0,
        supportReviveGroup: true,
      } as Record<string, any>;

      // TODO if this.ctx.channelId是错误的, 应该是this.ctx.data.channelId --> 也不知道应用场景在哪, 不敢动...
      if (this.ctx.channelId > 0) {
        data.channelId = this.ctx.channelId;
      }

      if (this.ctx.data.groupId) {
        data.groupId = this.ctx.data.groupId;
      }

      if (this.ctx.data.selectedPromotions) {
        data.selectedPromotions = JSON.stringify(this.ctx.data.selectedPromotions);
      }

      const finallyFn = () => {
        this.ctx.data.isUpdatingCartGoodsList = false;
        Toast.clear();
        this.ctx.event.emit('stopPullDownRefresh');
        Tee.$native.stopPullDownRefresh && Tee.$native.stopPullDownRefresh();
        // 触发事件骨架屏消失
        /* #ifdef weapp */
        getApp().trigger('trade-cart-data-loaded');
        /* #endif */
      };
      // 支持商品套餐 --> 以后修改参数需注意上游prefetch函数也要增加参数
      data.supportCombo = true;
      data.excludedComboSubType = JSON.stringify([]);
      // 禁止后端查询有赞担保信息
      data.disableSearchYzGuarantee = true;

      /* #ifdef web */
      const pdlive = args.get('pdlive', window.location.href);
      if (pdlive) {
        data.pdlive = pdlive;
      }
      /* #endif */

      let func;

      /* #ifdef weapp */
      func = getPrefetchData({
        prefetchKey,
        normalFetchCb: () => {
          return requestV2({
            data,
            path: '/wsctrade/cartGoodstList.json',
            method: 'GET',
          });
        },
      });
      /* #endif */

      /* #ifdef web */
      func = requestV2({
        data,
        path: '/wsctrade/cartGoodstList.json',
        method: 'GET',
      });
      /* #endif */

      return func
        .then((result) => {
          finallyFn();

          // 将返回数据格式化为驼峰格式
          const camelCaseResult = mapKeysCase.toCamelCase(result);
          // 执行cartParser处理数据
          const currentShopCart = cartParser(camelCaseResult);
          const shopList = currentShopCart.shopList || [];
          const shopCart = shopList[0] || {};
          // 提交栏需要数据，这里是提交组件需要的数据
          this.ctx.data.submitData = {
            selectedPreferencePrice: shopCart.selectedPreferencePrice,
            selectedDiscountFee: shopCart.selectedDiscountFee,
            promotionDetails: shopCart.promotionDetails || [],
            isNewHopeShop: shopCart.isNewHopeShop,
            activities: shopCart.activities || [],
          };
          // 执行diff算法判断数据是否有差异
          const cachedData = this.getCachedCartData();
          const hasDataChanged = cartDataDiff(cachedData, camelCaseResult, scene);
          // const { log } = console;
          // log('hasDataChanged', hasDataChanged);
          // 缓存新的原始数据
          this.cacheCartData(camelCaseResult);
          // 如果数据没有变化，则不继续执行cartParser
          if (!isPull && cachedData && !hasDataChanged) {
            asyncDataHelper.update(shopCart, this.ctx);
            return Promise.resolve();
          }

          this.ctx.data.kdtId = shopCart.kdtId || +getCurrentKdtId();
          this.ctx.data.shopTitle = shopCart.shopName || '';
          // 标识一下确实返回了
          this.ctx.data.hasResponseValidGoods = (shopCart.goodsGroupList || []).some(
            (goodsGroup) => !!goodsGroup.goodsList.length
          );
          // 已经授权，就正常赋值，展示未失效商品列表
          if (this.ctx.data.isAuthProtocol) {
            this.ctx.data.shopList = shopList;

            // 3. 如果本次更新存在markId，并且全局的markId不等于本次更新的markId，就认为是过期，本次不更新数据
            const isExpired = markId && this.ctx.data.globalMarkId !== markId;

            if (!isExpired) {
              this.setCartGoodsData({
                shopCart,
                unavailableItems: currentShopCart.unAvailableGoodsList || [],
              });
            }
          } else {
            // 没有授权，就暂时保存一下数据，后面授权成功之后再赋值
            this.tmpShopList = shopList;
            this.tmpShopCart = shopCart;
            this.tmpUnavailableItems = currentShopCart.unAvailableGoodsList || [];

            this.setCartGoodsData({
              shopCart: [],
              unavailableItems: [],
            });
            // 拉起授权弹窗
            this.authProtocol();
          }

          this.ctx.data.dataLoaded = true;

          // 判断是否存在营销返回可指定活动
          const { recommendPromotionInfoList: list = [] } = shopCart;
          if (list[0]) {
            this.ctx.data.recommendPromotionInfoList = formatPromotionInfoList(list[0]);
          }
          /* #ifdef weapp */
          // 当前路径不是下单页才会触发tabbar切换，为了处理购物车更改后不等接口返回立即点到下单页的场景
          // 此时判断当前页面是否是tab页，那么这种情况一定不是tabbar，导致购物车的按钮不显示
          const curPath = getApp().getCurrentPageOption()?.path || '';
          if (!curPath.includes('trade-buy')) {
            getApp()
              .isSwitchTab()
              .then((isTabPage) => {
                this.ctx.data.isTabPage = isTabPage;
              });
          }
          /* #endif */

          this.ctx.cloud.invoke('onGoodsListChange');

          /**
           * 购物车临时临时优化分批加载
           * 将空购物车提示、失效商品等后置至 post
           * 如果购物车存在 6 个及以上有效商品，延迟加载 post
           */
          if (isPageFirstFetch) {
            this.onPageFirstLoad();
          }
          monitorLogger.end({ name: loggerKey.cart_page });
        })
        .then(() => {
          // eslint-disable-next-line @youzan/ranta-cloud/valid-ctx-cloud
          this.ctx.cloud.invoke('onCartRefresh');
        })
        .catch((e) => {
          monitorLogger.end({
            name: loggerKey.cart_page,
            type: 'error',
            level: 'error',
            extra: { message: JSON.stringify(e, ['message', 'stack']) },
          });
          finallyFn();
          if (isPageFirstFetch) {
            this.onPageFirstLoad();
          }
          // TODO: 临时处理，网关限流，根据返回的html文案判断
          if (e?.includes?.('当前参与人数过多')) {
            /* #ifdef weapp */

            wx.showToast({
              title: '店铺太火爆啦，请稍后重试',
              icon: 'none',
              duration: 3000,
            });
            /* #endif */
            /* #ifdef web */
            Toast('店铺太火爆啦，请稍后重试');
            /* #endif */
          }
          return Promise.reject(e);
        });
    });
  }

  onPageFirstLoad() {
    this.ctx.inited();
    /* #ifdef weapp */
    setTimeout(() => {
      // 处理白屏
      getApp().trigger('first-render', 'cart');
    }, 400);
    const hasDesign = getHasDesign(this.ctx.cloud.hasDesign, ['trade-buy', 'buy']);
    if (!hasDesign) {
      import('@youzan/wsc-tee-trade-common/lib/biz/trade-buy-prerender/set-cache').then(
        ({ initPrerenderCache }) => {
          initPrerenderCache();
        }
      );
    }
    /* #endif */
  }

  setOrderKeepData(data) {
    const { displayData = {}, orderData = {} } = data || {};
    const { bookKey } = orderData;
    if (bookKey === this.ctx.data.bookKey) {
      this.ctx.data.displayData = displayData;
      this.ctx.data.orderData = orderData;
      this.ctx.event.emit('ORDER_KEEP:open');
    }
  }

  getPresentCanSelect() {
    /* #ifdef web */
    this.ctx.data.canSelectPresent = true;
    /* #endif */
    /* #ifdef weapp */
    return getWeappShuntConfig().then((res) => {
      const { order = false } = res || ({} as any);
      this.ctx.data.canSelectPresent = order;
    });
    /* #endif */
  }

  initProcess() {
    mapProcess(this, {
      authProtocol: () => {
        this.authProtocol().then((isAuth) => {
          if (isAuth) {
            this.getCartGoodsList();
          }
        });
      },
      navigateFromCart: ({ link, navigateType = '', znbType = '' }) => {
        /* #ifdef web */
        // @ts-ignore
        ZNB.init({
          // @ts-ignore
          kdtId: window._global?.kdtId,
          // @ts-ignore
        }).catch((e) => {
          console.log(e);
        });
        const {
          isAlipayApp = false,
          isQQApp = false,
          isXhsApp = false,
          isTTApp = false,
          isSwanApp = false,
          isKsApp = false,
        } = get(window, '_global.miniprogram', {});
        if (isAlipayApp || isQQApp || isXhsApp || isTTApp || isSwanApp || isKsApp) {
          navigate({
            web: {
              type: 'znb',
              znb: {
                aliappUrl: link,
                qqUrl: link,
                xhsUrl: link,
                ttUrl: link,
                swanUrl: link,
                ksUrl: link,
                type: znbType,
              },
            },
          });
          return;
        }

        // 私域直播间来源的购物车，禁止跳到商详&私域直播间半屏打开的购物车页，下单页跳转通知父组件
        const isPdLive = !!window._global?.pdlive;
        const isLiveHalfPage = args.get('isLiveHalfPage', window.location.href);
        if (isPdLive) {
          const goodsPageRegex = /\/wscgoods\/detail/;
          const isGoodsPage = goodsPageRegex.test(link);
          if (isGoodsPage) {
            return;
          }
          const tradePageRegexV1 = /\/pay\/wsctrade_buy/;
          const tradePageRegexV2 = /\/pay\/wscvis_buy/;
          const isTradePage = tradePageRegexV1.test(link) || tradePageRegexV2.test(link);
          if (isLiveHalfPage && isTradePage) {
            parent.postMessage(
              JSON.stringify({
                action: 'jumpOrderPage',
                url: link,
              }),
              // 商家独立域名后，这里不太好获取父页面域名，所以直接通配
              '*'
            );
            return;
          }
        }

        /* #endif */
        Tee.navigate({ url: link, type: navigateType });
      },
      setGroupId: (groupId) => {
        this.ctx.data.groupId = groupId;
        this.getCartGoodsList();
      },
      updateCartList: () => {
        return this.getCartGoodsList();
      },
      beforeCartClearHook: (payload: ClearCartParams) => {
        return this.ctx.cloud.invoke('beforeCartClear', payload);
      },
    });
  }

  initEvents() {
    mapEvent(this, {
      updateCartGoodsList: ({ scene, noToast, markId } = {}) => {
        // 非营销指定活动的更新购物车操作 需要清空选中的营销活动
        this.ctx.data.selectedPromotions = null;
        this.getCartGoodsList({ noToast: noToast || scene === 'numChange', scene, markId }).then(
          () => {
            this.ctx.event.emit('cartGoodsListDidUpdate', { scene });
          }
        );
      },
      updateCartGoodsListWithActivity: ({ selectedPromotions, scene } = {}) => {
        if (selectedPromotions) {
          this.ctx.data.selectedPromotions = selectedPromotions;
        }
        this.getCartGoodsList().then(() => {
          this.ctx.event.emit('cartGoodsListDidUpdate', { scene });
        });
      },
      updateCartAsyncData: () => {
        asyncDataHelper.update(this.ctx.data.shopCart, this.ctx);
      },
    });
  }

  initDataWatch() {
    mapData(this, ['themeColors'], {
      isSetData: false,
      callback: () => {
        this.ctx.data.themeStyle = this.getThemeStyle();
      },
    });
    mapData(this, ['currentAddress', 'currentLocation'], {
      isSetData: false,
      callback: () => {
        asyncDataHelper.update(this.ctx.data.shopCart, this.ctx);
      },
    });
  }

  toastStatus() {
    setTimeout(() => {
      const { repurchaseCouponStatus } = this.ctx.data;
      if (repurchaseCouponStatus === undefined) return;
      if (repurchaseCouponStatus === '0') {
        const { couponValue = '', couponUnit = '' } = this.ctx.data;
        return Toast(`已为你领取${couponValue}${couponUnit}优惠券，下单享优惠`);
      }
      couponCodeMap[repurchaseCouponStatus] && Toast(couponCodeMap[repurchaseCouponStatus]);
    }, 5000);
  }

  getThemeStyle() {
    const { themeColors = {} } = this.ctx.data;

    // 不要因为优化而调整这两个reduce，分开是刻意为之，方便调试
    const colors = Object.keys(themeColors).reduce((prev, key) => {
      return {
        ...prev,
        [`--${key}`]: themeColors[key],
      };
    }, {});
    const themeStyle = Object.keys(colors).reduce((prev, key) => {
      return `${prev}${key}:${colors[key]};`;
    }, '');

    return themeStyle;
  }

  initCloudData() {
    const handleShopCartUpdatedCallback = () => {
      const { shopCart } = this.ctx.data;
      const newOpenData = {
        goodsGroupList: cloudData.getGoodsGroupList({ shopCart }),
      };
      Object.keys(newOpenData).forEach((key) => {
        const newVal = newOpenData[key];
        if (!isSameObject(newVal, this[key])) {
          this[key] = newVal;
        }
      });
    };
    mapData(this, ['shopCart'], {
      isSetData: false,
      callback: handleShopCartUpdatedCallback,
    });

    mapEvent(this, {
      shopCartUpdated: handleShopCartUpdatedCallback,
    });

    mapData(this, ['themeColors'], {
      isSetData: false,
      callback: () => {
        const { themeColors } = this.ctx.data;
        const newOpenData = {
          themeColors: cloudData.getThemeColors({ themeColors }),
        };
        Object.keys(newOpenData).forEach((key) => {
          const newVal = newOpenData[key];
          if (!isSameObject(newVal, this[key])) {
            this[key] = newVal;
          }
        });
      },
    });
  }
}
