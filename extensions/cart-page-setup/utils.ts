import { cdnImage } from '@youzan/tee-biz-util';
import getGoodsPropertiesStr from '@youzan/wsc-tee-trade-common/lib/order-utils/goods/getGoodsPropertiesStr';
import hexToRgba from '@youzan/utils/string/hexToRgba';
import type {
  TradeCartGoods,
  TradeCartGoodsGroup,
  CartActivityInfo,
} from '@youzan-cloud/cloud-biz-types';
import { getSkuStr, formatSku } from './sku';
import type { ThemeColors } from './types';
import money from '@youzan/weapp-utils/lib/money';
import { getSystemInfoSync } from '@youzan/tee-api';
import get from '@youzan/utils/object/get';

export { getSkuStr } from './sku';

// 更新赠品SKU数据的工具函数
function updatePresentSkuData(shopCart) {
  /**
   * 考虑到新老赠品的兼容逻辑，对于老赠品的skuId不进行修改，
   * 只在goodsSkuInfoList里面选中，所以需要前端再新版本中对skuId做一个覆盖操作
   * 前端的变更都根据skuId来处理
   * 后端做兼容的成本比前端更大，所以放在前端处理
   */
  const handleGoodsSku = (group) => {
    // 做个前置安全判断
    if (!group?.groupActivityInfo?.selectablePresents) {
      return group;
    }

    // 改写skuId，老赠品没有goodsSkuInfoList，所以用原本的skuId和sku信息兜底
    group.groupActivityInfo.selectablePresents = group.groupActivityInfo.selectablePresents.map(
      (present) => {
        const selectedSku = present.goodsSkuInfoList?.find((i) => i.isSelected) ?? present;
        return {
          ...present,
          sku: selectedSku.sku,
          skuId: selectedSku.skuId,
        };
      }
    );
    return group;
  };

  if (shopCart.goodsGroupList) {
    shopCart.goodsGroupList = shopCart.goodsGroupList?.map((group) => {
      if (group?.groupActivityInfoList?.length > 0) {
        group.groupActivityInfoList = group.groupActivityInfoList.map((item) =>
          handleGoodsSku(item)
        );
      } else {
        group = handleGoodsSku(group);
      }

      return group;
    });
  }
  return shopCart;
}

// 获取商品sku 和属性信息
export function getGoodsSkuProperty(goods) {
  const skuStr = getSkuStr(goods.sku);
  const propertiesStr = getGoodsPropertiesStr(goods.properties);

  return [skuStr, propertiesStr].filter((item) => !!item).join('，');
}

// 获取商品 unique
function getUnique(goods) {
  const goodsPropertiesIds = goods.propertyIds || [];
  return `${goods.kdtId}-${goods.goodsId}-${goods.skuId}-${
    goods.activityId || 0
  }-${goodsPropertiesIds.join('-')}`;
}

function getTariffPriceText({ tariffRule, tariffPrice, num }) {
  if (tariffRule === 0) {
    return `预计 ￥ ${((+tariffPrice * num) / 100).toFixed(2)}`;
  }
  if (tariffRule === 1) {
    return '商品已含税';
  }

  return '';
}

// 商品计算属性格式化函数
export function formatGoodsComputedProperties(goods) {
  const DRUG_QUALITY_LIMIT = 10000;
  const GOODS_TAG_MAP = {
    HAITAO: cdnImage('public_files/2019/08/19/fbd4c38994578e951ef1cdfd9104606d.png'),
    PERIOD_BUY: cdnImage('public_files/2019/08/19/aea27fff45f6edb02bfd31c0b7ff3f04.png'),
    MEMBER_DISCOUNT: cdnImage('cdn/FkhVnpHh7ZwFAvBaUwO8B0F2Gf4V-1.png'),
    IS_DRUG_GOOD: cdnImage('path/to/cdn/dir/isDrugTag_3x.png'),
  };

  // 计算商品最大购买量
  const goodsMaxNum = goods.limitNum > 0 ? goods.limitNum : goods.stock;

  // 计算商品限购数量（考虑处方药限制）
  const goodsLimitNum =
    get(goods, 'bizExtension.cartBizMark.IS_PRESCRIPTION_DRUG') === '1'
      ? Math.min(DRUG_QUALITY_LIMIT, goodsMaxNum)
      : goodsMaxNum;

  // 计算库存提示文本
  const stockLimitText = goods.isShowStockShort
    ? '库存紧张'
    : goods.isShowStockNum
    ? `仅剩${goods.stock}件`
    : '';

  // 计算商品标签列表
  const goodsTagList = [];
  const { activityTag, isInstallment, bizExtension = {} } = goods;

  if (get(bizExtension, 'cartBizMark.PRE_SALE_TYPE') === '0') {
    goodsTagList.push('预售');
  }

  if (activityTag === '会员折扣') {
    goodsTagList.push('会员价');
  }

  if (isInstallment) {
    goodsTagList.push('分期支付');
  }

  if (activityTag && activityTag !== '会员折扣') {
    goodsTagList.push(activityTag);
  }

  // 计算起售和限购描述
  const startSaleNumAndLimitDesc =
    goods.startSaleNum > 1
      ? `${goods.startSaleNum}件起售${goods.limitNum ? `，限购${goods.limitNum}件` : ''}`
      : '';

  // 计算降价描述
  const cutPriceDesc = !+goods.cutPrice ? '' : `比加入时便宜${money(goods.cutPrice).toYuan()}元`;

  // 计算加价购商品标识（用于其他计算）
  const isPlusBuyGoods = +goods.activityType === 24;

  // 计算是否显示步进器
  const isCanStepperFormater = !isPlusBuyGoods;

  // 计算是否未开售
  const nowTime = new Date().getTime() / 1000;
  const isNotStartSold = !!(goods.startSoldTime && goods.startSoldTime > nowTime);

  // 计算商品标题标签
  let goodsTitleTag = '';
  if (get(goods, 'bizExtension.cartBizMark.IS_PRESCRIPTION_DRUG') === '1') {
    goodsTitleTag = GOODS_TAG_MAP.IS_DRUG_GOOD;
  } else {
    goodsTitleTag = GOODS_TAG_MAP[get(goods, 'settlementRule.settlementMark')] || '';
  }

  // 计算商品结算标记
  const goodsSettlementMark = get(goods, 'settlementRule.settlementMark') || '';

  // 计算组合商品详情
  const comboDetail = [];
  if (get(goods, 'comboDetail.groupList.length')) {
    goods.comboDetail.groupList.forEach(({ subComboList }) => {
      subComboList.forEach(({ num, thumbUrl }) => {
        comboDetail.push({
          src: thumbUrl,
          num,
        });
      });
    });
  }
  // 计算是否会员折扣商品
  const isMemberDiscount = goods.activityTag === '会员折扣';

  // 计算是否教育iOS在线商品
  const { platform } = getSystemInfoSync();
  const isEduIosOnlineGoods =
    platform === 'ios' &&
    goods.goodsType === 31 &&
    get(goods, 'bizExtension.cartBizMark.isOnlineCourse') === '1';

  return {
    goodsSettlementMark,
    comboDetailFormat: comboDetail,
    goodsTitleTag,
    goodsTagList,
    stockLimitText,
    goodsMaxNum,
    goodsLimitNum,
    startSaleNumAndLimitDesc,
    cutPriceDesc,
    isCanStepperFormater,
    isNotStartSold,
    presaleDate: get(goods, 'bizExtension.cartBizMark.PRE_SALE_DATE') || '',
    isMemberDiscount,
    isEduIosOnlineGoods,
  };
}

export function cartParser(rawData) {
  const unAvailableGoodsList = [];

  // 移除内部转驼峰逻辑，由调用方负责格式化
  const rawDataCamel = rawData;

  const allGoodsIds = [];
  const shopList = (rawDataCamel || []).map((item) => {
    const {
      unavailableItems,
      activities,
      shopName,
      kdtId,
      selectedPreferencePrice = 0,
      goodsGroupList = [],
      isShowEstimatedPrice,
      isNewHopeShop = false,
      ...rest
    } = item;

    // 购物车商品图片填充格式改为aspectFill
    const goodsImgMode = 'aspectFill';
    let goodsCount = 0;
    const goodsGroupListFormated = goodsGroupList.map((goodsGroup = {} as any, i) => {
      const goodsList = (goodsGroup?.goodsList || []).map((item) => {
        const { activityId, goodsId, comboDetail } = item;
        if (allGoodsIds.indexOf(goodsId) === -1) {
          allGoodsIds.push(goodsId);
        }
        goodsCount++;

        const result = {
          // 商品套餐的数据要加上
          comboDetail,
          cartId: item.cartId,
          kdtId: item.kdtId,
          channelId: item.channelId,
          canyinId: item.canyinId,
          goodsId,
          skuId: item.skuId,
          storeId: item.storeId,
          title: item.title,
          num: item.num,
          stock: item.stockNum,
          activityStartTime: item.activityStartTime, // 活动开始时间
          activityEndTime: item.activityEndTime, // 活动结束时间
          isShowStockNum: item.isShowStockNum, // 是否显示剩余数量
          isShowStockShort: item.isShowStockShort, // 是否显示库存紧张
          limitNum: item.limitNum || 0,
          cutPrice: item.cutPrice, // 比加入时降价
          estimatedPrice: typeof item.estimatedPrice === 'number' ? item.estimatedPrice : '',
          price: item.payPrice,
          payPrice: item.payPrice,
          originPrice: item.pointsPrice ? 0 : item.originPrice,
          sku: getGoodsSkuProperty(item),
          skuData: formatSku(item.sku),
          propertyIds: item.propertyIds,
          checked: !!item.selectState,
          maxNum: +item.limitNum ? Math.min(item.limitNum, item.stockNum) : item.stockNum,
          attachmentUrl: item.attachmentUrl,
          imgUrl: cdnImage(item.attachmentUrl, '!300x300.jpg'),
          unique: getUnique(item),
          extraAttribute: item.extraAttribute || '{}',
          messages: item.messages || '{}',
          alias: item.alias,
          logisticsTypeList: item.logisticsTypeList,
          goodsType: item.goodsType,
          activityId,
          weight: item.weight,
          activityType: item.activityType,
          activityAlias: item.activityAlias || '',
          activityTag: item.activityTag || '',
          activityTypeStr: item.activityTypeStr || '',
          isInstallment: item.isInstallment || false,
          settlementRule: item.settlementRule,
          tariffPrice: item.tariffPrice,
          tariffPriceText: getTariffPriceText(item),
          tariffRule: item.tariffRule,
          properties: item.properties || [],
          createdTime: item.createdTime,
          updatedTime: item.updatedTime,
          deliverTime: item.deliverTime,
          startSaleNum: item.startSaleNum || 0,
          revive: item.revive || false, // 是否是待复活商品
          isSevenDayUnconditionalReturn: item.isSevenDayUnconditionalReturn || false, // 七天无理由退货
          yzGuarantee: item.yzGuarantee || false, // 有赞放心购
          hideGuarantee: item.hideGuarantee || false, // 黑金隐藏有赞放心购
          startSoldTime: item.startSoldTime || '', // 开售时间
          disableSelectMsg: item.disableSelectMsg || '',
          bizExtension: item.bizExtension,
          birthdayRelation: item.birthdayRelation || {},
          quotaCycle: item.quotaCycle,
          imgMode: goodsImgMode,
          pointsPrice: item.pointsPrice,
          pointsOriginPrice: item.pointsOriginPrice,
        };
        // 计算商品计算属性
        const computedProperties = formatGoodsComputedProperties(result);
        return {
          ...result,
          ...computedProperties,
        };
      });

      return {
        ...goodsGroup,
        goodsList,
        uniqId: i,
      };
    });

    (unavailableItems || []).forEach((item) => {
      const imgUrl = item.attachmentUrl || cdnImage('v2/image/wap/no-pic-v2.png');
      const activityId = item.activityId || 0;
      unAvailableGoodsList.push({
        cartId: item.cartId,
        kdtId: item.kdtId,
        channelId: item.channelId,
        goodsId: item.goodsId,
        goodsType: item.goodsType,
        skuId: item.skuId,
        storeId: item.storeId,
        productSkuId: item.skuId,
        title: item.title || '',
        messages: item.messages || '{}',
        price: item.payPrice,
        payPrice: item.payPrice,
        originPrice: item.originPrice,
        sku: getSkuStr(item.sku),
        num: item.num,
        errorMsg: item.errorMsg,
        imgUrl: cdnImage(imgUrl, '!300x300.jpg'),
        unique: getUnique(item),
        alias: item.alias,
        activityId,
        activityType: item.activityType,
        activityTag: item.activityTag === '加价购' ? '换购' : item.activityTag || '',
        imgMode: goodsImgMode,
      });
    });
    return {
      kdtId,
      shopName,
      isNewHopeShop, // 是否是新希望店铺
      goodsGroupList: goodsGroupListFormated,
      goodsCount, // 商品总数
      activities,
      isShowEstimatedPrice,
      selectedPreferencePrice, // 活动优惠金额
      ...rest,
    };
  });

  // 处理赠品SKU数据
  const processedShopList = shopList.map((shop) => updatePresentSkuData(shop));

  return {
    shopList: processedShopList,
    unAvailableGoodsList,
    allGoodsIds,
  };
}
const goodsTypeEnum = {
  /** 普通类型商品 goodsType = 0 */
  NORMAL: [0, 'normal'],
  /** 周期购 goodsType = 24 */
  PERIOD_BUY: [24, 'periodBuy'],
  /** 知识付费商品 goodsType = 31 */
  KNOWLEDGE: [31, 'knowledge'],
  /** 酒店商品 goodsType = 35 */
  HOTEL: [35, 'hotel'],
  /** 普通虚拟商品 goodsType = 182 */
  VIRTUAL: [182, 'virtual'],
  /** 电子卡券商品 goodsType = 183 */
  ECARD: [183, 'ecard'],
  /** 分销商品 goodsType = 10 */
  FENXIAO: [10, 'fenxiao'],
  /** 拍卖商品 goodsType = 1 */
  AUCTION: [1, 'auction'],
  /** 会员卡商品 goodsType = 20 */
  MEMBER_CARD: [20, 'memberCard'],
  /** 礼品卡商品 goodsType = 21 */
  GIFT_CARD: [21, 'giftCard'],
  /* 餐饮商品 goodsType = 5 */
  FOOD: [5, 'food'],
  /* 次卡商品 goodsType = 22 */
  TIME_CARD: [22, 'timeCard'],
  /* 有赞会议商品 goodsType = 23 */
  MEETINGS: [23, 'meetings'],
  /* 收银台商品 goodsType = 30 */
  CASHIER_VIRTUAL: [30, 'cashierVirtual'],
  /* 酒店套餐商品 goodsType = 185 */
  HOTEL_PACKAGE: [185, 'hotelPackage'],
  /* 普通服务类商品 goodsType = 40 */
  SERVICE: [40, 'service'],
  /* 卡项商品 goodsType = 71 */
  CARD_ITEM: [71, 'cardItem'],
  /* 混合类型 goodsType = 184 */
  MIXED: [184, 'mixed'],
  /* 外部会员卡商品 goodsType = 201 */
  OUT_MEMBER_CARD: [201, 'outMemberCard'],
  /* 外部直接收款商品 goodsType = 202 */
  OUT_CASH: [202, 'outCash'],
  /* 外部普通商品 goodsType = 203 */
  OUT_COMMON: [203, 'outCommon'],
  /* 外部服务商品 goodsType = 204 */
  OUT_SERVICE: [204, 'outService'],
  /* mock不存在商品 goodsType = 205 */
  MOCK: [205, 'mock'],
  /* 小程序二维码 goodsType = 206 */
  WX_QRCODE: [206, 'wxQrcode'],
  /* 积分充值商品 goodsType = 207 */
  POINT_PURCHASE: [207, 'pointPurchase'],
  /* 付费优惠券商品 goodsType = 208 */
  PAY_COUPONS: [208, 'payCoupons'],
  /* 商品化商品类型 goodsType = 80 */
  COMMERCIAL_SALE_ITEM: [80, 'commercialSaleItem'],
};
export const getGoodsTypeStr = (goodsType: number) =>
  Object.values(goodsTypeEnum).find((item) => item[0] === goodsType)?.[1];
export const isSameObject = (a, b) => JSON.stringify(a) === JSON.stringify(b);

// 需要过滤的key，以下key为动态改变，但又不会影响列表展示的字段
export const FILTER_KEYS = [
  'updatedTime',
  'estimatedPrice',
  'selectedPreferencePrice',
  'selectedDiscountFee',
  'promotionDetails',
  'activities',
];
/**
 * 购物车数据diff算法
 * 比较两个购物车数据对象是否有差异
 * @param oldData - 旧数据
 * @param newData - 新数据
 * @param scene - 场景值，用于判断需要过滤的字段
 * @returns 是否有差异
 */
export function cartDataDiff(oldData: any, newData: any, scene?: string): boolean {
  if (!oldData && !newData) return false;
  if (!oldData || !newData) return true;

  // 深拷贝数据，避免影响原始数据
  const oldCopy = JSON.parse(JSON.stringify(oldData));
  const newCopy = JSON.parse(JSON.stringify(newData));

  // 过滤不需要比较的字段
  const filterFields = (data: any) => {
    if (Array.isArray(data)) {
      return data.map((item) => filterFields(item));
    }
    if (data && typeof data === 'object') {
      const filtered = { ...data };
      // 始终过滤的字段
      FILTER_KEYS.forEach((key) => {
        delete filtered[key];
      });

      // 根据scene过滤特定字段
      if (scene === 'selectedChange') {
        delete filtered.selectState;
      }
      if (scene === 'numChange') {
        delete filtered.num;
      }

      // 递归处理嵌套对象
      Object.keys(filtered).forEach((key) => {
        filtered[key] = filterFields(filtered[key]);
      });

      return filtered;
    }
    return data;
  };

  const filteredOld = filterFields(oldCopy);
  const filteredNew = filterFields(newCopy);

  return JSON.stringify(filteredOld) !== JSON.stringify(filteredNew);
}
export const cloudData = {
  getThemeColors({ themeColors = {} as Record<string, any> }): ThemeColors {
    return {
      themeMainBgColor: themeColors['main-bg'],
      themeMainBgAlpha10Color: hexToRgba(themeColors.general, 0.1),
    };
  },
  getGoodsGroupList({ shopCart }): TradeCartGoodsGroup[] {
    return (shopCart?.goodsGroupList || []).map((item) => {
      const goodsList: TradeCartGoods[] = item.goodsList.map((item) => {
        return {
          id: item.goodsId,
          imgUrl: item.imgUrl,
          type: getGoodsTypeStr(item.goodsType),
          payPrice: item.payPrice,
          originPrice: item.originPrice,
          title: item.title,
          alias: item.alias,
          price: item.price,
          weight: item.weight || 0,
          cartId: item.cartId,
          activityId: item.activityId,
          activityType: item.activityType,
          num: item.num,
          storeId: item.storeId,
          skuId: item.skuId,
          sku: item.sku,
          messages: item.messages,
          isSelected: item.checked,
        };
      });
      const { groupActivityInfo = null } = item;
      const activityInfo: CartActivityInfo | null = groupActivityInfo
        ? {
            id: groupActivityInfo.activityId,
            url: groupActivityInfo.activityUrl,
            conditionPrice: groupActivityInfo.conditionPrice || 0,
            desc: groupActivityInfo.activityDesc,
            alias: groupActivityInfo.activityAlias,
            duration: groupActivityInfo.activityDuration,
            meet: groupActivityInfo.meet,
            type: groupActivityInfo.activityType,
            tags: groupActivityInfo.activityTags,
          }
        : null;
      return {
        goodsList,
        activityInfo,
      };
    });
  },
};

// 格式化营销返回活动
export const formatPromotionInfoList = (promotionInfo) => {
  const { activityId, activityType, promotionTag, sendBenefitInfo = {} } = promotionInfo;
  const { sendPointsGifts = [], sendCouponsGifts = [], sendPresentInfo = {} } = sendBenefitInfo;
  const newCouponList = sendCouponsGifts.map((item) => {
    const { couponTemplate = {} } = item;
    return {
      ...couponTemplate,
      extraInfo: couponTemplate.extra,
      id: item.couponTemplateId,
      num: item.num,
      // 购物车指定营销活动处要求优惠券不展示描述
      description: '',
    };
  });
  const umpSendPromotionInfo = {
    activityId,
    activityType,
    coupons: newCouponList.filter((item) => item.activityTypeGroup === 1),
    couponsCode: newCouponList.filter((item) => item.activityTypeGroup === 2),
    score: sendPointsGifts[0]?.num,
    presents: sendPresentInfo.allPresents,
    canChoosePresentNum: sendPresentInfo.choosePresentNum,
  };
  return {
    activityId,
    includeActivityType: activityType,
    promotionTag,
    umpSendPromotionInfo,
  };
};
