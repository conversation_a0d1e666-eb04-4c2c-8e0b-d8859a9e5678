<template>
  <view v-if="shopCart.goodsGroupList" class="valid-goods" :style="themeStyle">
    <van-list
      :re-init-observer="listObNeedReLoad"
      :loading="loadLoading"
      :finished="finished"
      finished-text=" "
      @load="getLoadGoodsList"
      @ob-reloaded="obReload"
      :immediate-check="true"
    >
      <view
        class="goods-group"
        v-for="(goodsGroup, goodsGroupIndex) in loadGoodsList"
        :key="goodsGroup.uniqId"
      >
        <goods-group-item
          :goods-group="goodsGroup"
          :goods-group-index="goodsGroupIndex"
          :goods-count="shopCart.goodsCount"
          @change-goods-sku="handleChangeGoodsSku"
          @refresh-cart-goods-list="refreshCartGoodsList"
          @on-present-popup-show="handleChangePresentPopup"
          @change-present-sku="handleChangePresentSku"
          :cloud-activity-id="
            (goodsGroup.groupActivityInfo && goodsGroup.groupActivityInfo.activityId) || 0
          "
          :cloud-index="goodsGroupIndex"
        />

        <van-dialog ref="van-dialog" />
      </view>
    </van-list>
    <combo-detail-popup ref="combo-detail-popup" />
    <!-- 兼容原生购物车定制活动弹窗 -->
    <ump-info-popup
      :show-popup="cartActivityPopupShow"
      :activity-info="cartActivityInfo"
      @close="handleCloseCartActivityPopup"
    />
    <goods-group-ump
      :only-show-exchange="true"
      :kdt-id="kdtId"
      :offline-id="offlineId"
      :is-editing="isEditing"
      :goods-list="currentActivityGoodsList"
      :checked-goods-list="checkedGoodsList"
      :activity-info="currentActivityInfo"
      :theme-general-color="themeGeneralColor"
      :theme-general-alpha-10-color="themeGeneralAlpha10Color"
      :theme-css="themeCSS"
      :theme-colors="themeColors"
      :logger="logger"
      @refresh-cart-goods-list="refreshCartGoodsList"
    />
  </view>
</template>
<script>
import VanToast from '@youzan/vant-tee/dist/toast/index.vue';
import List from '@youzan/vant-tee/dist/list/index';
import VanDialog from '@youzan/vant-tee/dist/dialog/index.vue';
import { mapState, mapActions } from '@ranta/store';

export default {
  components: {
    'van-dialog': VanDialog,
    'van-toast': VanToast,
    // 'combo-detail-popup': ComboDetailPopup,
    'van-list': List,
  },

  data() {
    const { logger } = this.ctx;
    return {
      logger,
      isShowCustomGoodsListHeader: this.ctx?.widgets?.CustomGoodsListHeader,
      isShowCustomGoodsItem: this.ctx?.widgets?.CustomGoodsItem,
      ...mapState(this, [
        'shopCart',
        'kdtId',
        'offlineId',
        'checkedGoodsList',
        'themeCSS',
        'themeColors',
        'themeGeneralColor',
        'themeGeneralAlpha10Color',
        'loadGoodsList',
        'loadLoading',
        'finished',
        'isEditing',
        'themeStyle',
        'listObNeedReLoad',
        'cartActivityPopupShow',
        'cartActivityInfo',
        'currentActivityInfo',
        'currentActivityGoodsList',
      ]),
    };
  },
  mounted() {
    this.initActions();
    this.handleStopLoading();
    this.initPageStyle();
  },
  methods: {
    obReload() {
      this.$nextTick(() => {
        this.updateListObNeedReLoad(false);
      });
    },
    initPageStyle() {
      const cssList = [
        ['--stepper-input-margin-right', 'var(--theme-page-stepper-input-margin-right)'],
        ['--stepper-input-margin-left', 'var(--theme-page-stepper-input-margin-left)'],
        ['--stepper-border-width', 'var(--theme-page-stepper-border-width)'],
        ['--stepper-border-style', 'var(--theme-page-stepper-border-style)'],
        ['--stepper-border-color', 'var(--theme-page-stepper-border-color)'],
        ['--stepper-background-color', 'var(--theme-page-stepper-background-color)'],
        [
          '--stepper-background-color-disabled',
          'var(--theme-page-stepper-background-color-disabled)',
        ],
        ['--stepper-border-top-left-radius', 'var(--theme-page-stepper-border-top-left-radius)'],
        [
          '--stepper-border-bottom-left-radius',
          'var(--theme-page-stepper-border-bottom-left-radius)',
        ],
        ['--stepper-border-top-right-radius', 'var(--theme-page-stepper-border-top-right-radius)'],
        [
          '--stepper-border-bottom-right-radius',
          'var(--theme-page-stepper-border-bottom-right-radius)',
        ],
      ];
      const cssStr = cssList
        .map((v) => {
          return `${v[0]}:${v[1]};`;
        })
        .join('');
      if (this.$root.setPageStyle) {
        this.$root.setPageStyle(cssStr);
      }
      /* #ifdef weapp */
      // 下拉刷新背景色和页面背景色保持一致
      wx.setBackgroundColor({
        backgroundColor: '#f7f8fa',
      });
      /* #endif */
    },
    initActions() {
      mapActions(this, [
        'handleChangePresentPopup',
        'getLoadGoodsList',
        'handleStopLoading',
        'refreshCartGoodsList',
        'handleChangeGoodsSku',
        'handleChangePresentSku',
        'handleHideSkuShow',
        'updateListObNeedReLoad',
        'handleCloseCartActivityPopup',
      ]);
    },
  },
};
</script>

<style scoped lang="scss">
.goods-group {
  position: relative;
  margin-top: var(--theme-page-card-margin-top, 0);
  margin-bottom: var(--theme-page-card-margin-bottom, 12px);
  margin-right: var(--theme-page-card-margin-right, 12px);
  margin-left: var(--theme-page-card-margin-left, 12px);
  border-radius: var(--theme-page-card-border-radius, 8px);
  overflow: hidden;
  background: #fff;
  /* mask-image: radial-gradient(white, black); */

  &__goods-item {
    /* overflow: hidden;
    mask-image: radial-gradient(white, black); */
  }
}
</style>
