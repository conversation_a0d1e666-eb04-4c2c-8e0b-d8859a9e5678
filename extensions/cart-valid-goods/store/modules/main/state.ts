/**
 * 业务执行流程
 */
import { getSystemInfoSync } from '@youzan/tee-api';

const { platform } = getSystemInfoSync();

export default {
  shopCart: {},
  editMode: 'submit',
  editCheckedGoods: {},
  themeCSS: '',
  themeColors: {},
  themeTag: {},
  themeStyle: '',
  onlineCourseUnselectFlag: false,
  isIOS: platform === 'ios',
  // 滚动加载
  loadGoodsList: [],
  loadLoading: true,
  finished: false,
  pageNum: 1,
  offlineId: 0,
  listObNeedReLoad: false,
  cartActivityPopupShow: false,
  cartActivityInfo: {},
  currentActivityInfo: {},
  currentActivityGoodsList: [],
  checkedGoodsList: [],
  // 计算出的数据，避免通过 getter 循环调用
  computedGoodsGroupList: [],
};
