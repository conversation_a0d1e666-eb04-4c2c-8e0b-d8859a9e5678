import hexToRgba from '@youzan/utils/string/hexToRgba';

export default {
  kdtId() {
    return this.shopCart.kdtId;
  },
  // 编辑模式启用 0为非编辑模式
  currentEditKdtId() {
    if (this.editMode === 'edit') {
      return this.shopCart.kdtId;
    }
    return 0;
  },
  themeGeneralColor() {
    return this.themeColors.general;
  },
  themeGeneralAlpha10Color() {
    return hexToRgba(this.themeGeneralColor, 0.1);
  },
  isEditing() {
    return !!this.currentEditKdtId;
  },
  showEstimatedPrice() {
    return this.shopCart.isShowEstimatedPrice;
  },
};
