/**
 * 监听数据变化，执行特定副作用函数
 */
export default function (store) {
  store.watch('shopCart', () => {
    store.afterShopCartUpdated();
    // shopCart变更后，执行loadGoodsList变更
    store.updateLoadGoodsList();
  });

  store.watch('editMode', () => {
    // 重置编辑模式选中状态
    store.resetEditCheckedGoods();
    store.updateProvideData();
  });
  store.watch('canSelectPresent', () => {
    store.getPresentListCookie();
  });
  store.watch('presentData', () => {
    store.updatePresentInfo();
  });
}
