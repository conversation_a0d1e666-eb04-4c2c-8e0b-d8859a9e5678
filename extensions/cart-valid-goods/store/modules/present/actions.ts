import { setStorageSync, getStorageSync } from '@youzan/tee-api';

// 赠品数据缓存key
const presentCookieKey = 'pick_present_activity_id_map';

export default function (ctx) {
  return {
    updatePresentInfo() {
      const SELECTED_PRESENTS = Object.keys(this.presentData).reduce((total, item) => {
        if (item !== 'IS_SELECT_PRESENT') {
          total = total.concat(this.presentData[item]);
        }
        return total;
      }, []);

      const IS_SELECT_PRESENT = this.presentData.IS_SELECT_PRESENT ? 1 : 0;

      ctx.data.presentData = {
        IS_SELECT_PRESENT,
        SELECTED_PRESENTS: IS_SELECT_PRESENT ? SELECTED_PRESENTS : [],
      };
    },
    // 获取缓存中赠品列表
    getPresentListCookie() {
      ctx.env.getQueryAsync().then((option) => {
        const query = this.canSelectPresent ? option || {} : {};
        const { selectablePresentNum, isUserSelectPresent, activityId } = query;
        let { presentIds = '[]' } = query;
        // 缓存挑选赠品
        let pickPresentCookieData = {};

        // 活动页面传入
        try {
          presentIds = JSON.parse(presentIds);
        } catch (e) {
          presentIds = [];
        }

        // 获取缓存挑选赠品
        try {
          pickPresentCookieData = getStorageSync(presentCookieKey) || {};
          const { saveTime } = pickPresentCookieData || ({} as any);
          if (new Date().valueOf() - saveTime > 1000 * 60 * 60 * 24) {
            // 存储时间超过一天，清空挑选赠品缓存
            pickPresentCookieData = {};
            setStorageSync(presentCookieKey, {});
          }
        } catch (e) {
          pickPresentCookieData = {};
        }

        if (
          activityId &&
          presentIds.length &&
          (isUserSelectPresent === '1' || isUserSelectPresent === 1)
        ) {
          // 活动页传入赠品处理
          pickPresentCookieData[activityId] = {
            skuIdList: presentIds,
            selectablePresentNum: Number(selectablePresentNum),
          };
          try {
            setStorageSync(presentCookieKey, {
              ...pickPresentCookieData,
              saveTime: new Date().valueOf(),
            });
          } catch (e) {}
        }

        this.pickPresentCookieData = pickPresentCookieData;
      });
    },
    // 修改是否展示挑选赠品弹窗
    handleChangePresentPopup({ goodsList, ...rest }) {
      let presentInfo: Record<string, unknown> = {};
      if (goodsList) {
        presentInfo.goodsList = goodsList;
      }
      presentInfo = {
        ...this.presentInfo,
        ...presentInfo,
        ...rest,
      };
      this.presentInfo = presentInfo;
      ctx.data.presentPopupInfo = presentInfo;
    },
    // 处理挑选的赠送商品
    handlePickPresent({ goodsList, activityId, selectablePresentNum }) {
      const pickPresentCookieData = {
        ...this.pickPresentCookieData,
        [activityId]: {
          skuIdList: goodsList.map(({ skuId }) => skuId),
          selectablePresentNum,
        },
        saveTime: new Date().valueOf(),
      };
      this.pickPresentCookieData = pickPresentCookieData;
      setStorageSync(presentCookieKey, pickPresentCookieData);
      this.updatePresentInfo();
    },

    // 修改赠品sku的回调
    handleChangePresentSkuCallback({ activityId, goodsData, skuData }) {
      const presentList = this.presentData[activityId] || [];
      const selectedSkuList = presentList.map((item) => {
        if (item.skuId === goodsData.skuId) {
          return skuData.id;
        }
        return item.skuId;
      });

      const pickPresentCookieData = {
        ...this.pickPresentCookieData,
        [activityId]: {
          skuIdList: selectedSkuList,
          selectablePresentNum: this.selectablePresentNumMap[activityId],
        },
        saveTime: new Date().valueOf(),
      };

      // 修改可选赠品列表的skuId & sku
      const presentGoodsList = this.presentInfo.goodsList || [];
      if (presentGoodsList.length) {
        const presentInfo = {
          ...this.presentInfo,
          goodsList: presentGoodsList.map((item) => {
            // 从skuList中拿sku信息
            const currentSku =
              goodsData.goodsSkuInfoList?.find((item) => item.skuId === skuData.id) ?? {};

            if (item.id === goodsData.id) {
              item.sku = currentSku.sku;
              item.skuId = currentSku.skuId;
            }
            return item;
          }),
        };

        ctx.data.presentPopupInfo = presentInfo;
      }

      // 保存到cookie当中，在presentMap的computed计算中会用到
      setStorageSync(presentCookieKey, pickPresentCookieData);

      this.pickPresentCookieData = pickPresentCookieData;
      // 更新sku信息，在提交购物车数据时用到
      this.updatePresentInfo();
      ctx.event.emit('cartPresentSku:afterSubmit');
    },

    handleChangePresentSku(data) {
      const params = {
        ...data,
        goods: {
          ...data.goods,
          presentSkuList: data.goods?.goodsSkuInfoList || [],
        },
      };
      this.currentShowSkuGoods = {
        ...params,
        goodsData: params.goods,
      };
      ctx.event.emit('cartPresentSku:fetch', params);
    },

    handlePresentSkuSelect({ selectedSkuComb }) {
      this.currentShowSkuGoods = {
        ...this.currentShowSkuGoods,
        skuData: selectedSkuComb,
      };
    },
  };
}
