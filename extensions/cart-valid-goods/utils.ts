import mapKeysToCamelCase from '@youzan/utils/string/mapKeysToCamelCase';
import { getSystemInfoSync } from '@youzan/tee-api';
import get from '@youzan/utils/object/get';
import pick from '@youzan/utils/object/pick';
import image from '@youzan/utils/browser/image';
import { cdnImage } from '@youzan/tee-biz-util';
import mapKeysToSnakeCase from '@youzan/utils/string/mapKeysToSnakeCase';
import { ShopList, GoodsList } from './types';

/** 判断是否是同个商品 */
export function isSameGoods(oldGoods, newGoods) {
  // 新版商品有cart_id，有赞云可能没传 cart_id
  if (newGoods.cart_id && oldGoods.cart_id) {
    return newGoods.cart_id === oldGoods.cart_id;
  }

  // 兼容没有activity_id  的情况
  if (!oldGoods.activity_id) oldGoods.activity_id = 0;
  if (!newGoods.activity_id) newGoods.activity_id = 0;
  return (
    oldGoods.goods_id === newGoods.goods_id &&
    oldGoods.sku_id === newGoods.sku_id &&
    oldGoods.canyin_id === newGoods.canyin_id &&
    oldGoods.activity_id === newGoods.activity_id
  );
}

// 传入单个商品判断是否是ios小程序教育线上商品
export function isEduIosOnlineGoods(goods) {
  const { platform } = getSystemInfoSync();
  return (
    platform === 'ios' &&
    goods.goodsType === 31 &&
    goods.bizExtension?.cartBizMark?.isOnlineCourse === '1'
  );
}

/**
 * 判断当前商品是否可选
 *
 * @export
 * @param {*} goods
 * @param {boolean} [editMode=false]  是否是编辑模式
 * @returns
 */
export function isGoodsCheckboxEnable(goods, editMode = false) {
  // 编辑模式都可选
  if (editMode) return true;

  // 不满足起售商品禁止选中
  if (goods.startSaleNum && goods.num < goods.startSaleNum) return false;

  // ios小程序教育线上课不可选
  if (isEduIosOnlineGoods(goods)) return false;

  // 待复活商品
  if (goods.revive) return false;

  // 待开售商品禁止选中
  const nowTime = new Date().getTime() / 1000;
  if (goods.startSoldTime && goods.startSoldTime > nowTime) return false;

  return true;
}

/** 取得商品在当前店铺中的索引 */
export function whereCurrentGoodsInShop(shop = { goodsGroupList: [] }, goods) {
  let goodsGroupIndex = -1;
  let goodsIndex = -1;

  goodsGroupIndex = (shop.goodsGroupList || []).findIndex((goodsGroup) => {
    goodsIndex = (goodsGroup.goodsList || []).findIndex((item) => isSameGoods(item, goods));
    return goodsIndex !== -1;
  });

  return {
    goodsGroupIndex,
    goodsIndex,
  };
}

/** 取得商品在当前店铺商品列表的索引 */
export function whereCurrentGoodsInList(list, goods) {
  const currentIndex = (list || []).findIndex((item) => {
    if (goods.cartId && item.cartId) {
      return goods.cartId === item.cartId;
    }
    // 兼容有赞云
    // 兼容没有activity_id  的情况
    item.activityId = item.activityId || 0;
    return (
      item.goodsId === goods.goodsId &&
      item.skuId === goods.skuId &&
      item.canyinId === goods.canyinId &&
      item.activityId === goods.activityId
    );
  });

  return currentIndex;
}

/** 获取商品属性 */
export function getGoodsPropertiesStr(properties = []) {
  const propertiesArr = [];

  mapKeysToCamelCase(properties).forEach((currentProperty) => {
    const propValueList = get(currentProperty, 'propValueList', []);
    propValueList.forEach((currentValue) => {
      propertiesArr.push(currentValue.propValueName);
    });
  });

  return propertiesArr.join('，');
}

/** 获取属性值 id 数组 */
export function getGoodsPropertiesIds(properties = []) {
  const propertiesIds = [];

  mapKeysToCamelCase(properties).forEach((currentProperty) => {
    const propValueList = get(currentProperty, 'propValueList', []);
    propValueList.forEach((currentValue) => {
      propertiesIds.push(currentValue.propValueId);
    });
  });

  return propertiesIds;
}

/**
 * 把sku 组件获取的数据格式化成下面格式
 * ```json
 * [
 *   {
 *     "prop_id": 131127,
 *     "prop_name": "杯型",
 *     "prop_value_list": [
 *       {
 *           "prop_value_id": 571382,
 *           "prop_value_name": "大杯"
 *       }
 *     ]
 *   }
 * ]
 * ```
 */
export function parseGoodsProperties(properties = []) {
  return mapKeysToCamelCase(properties).map((property) => {
    return {
      propId: property.kId,
      propName: property.k,
      propValueList: (property.v || []).map((value) => ({
        propValueId: value.id,
        propValueName: value.name,
      })),
    };
  });
}

export function getSkuStr(sku) {
  let parsedSku = [];
  try {
    parsedSku = JSON.parse(sku || '[]') || [];
  } catch (e) {
    console.warn(e);
  }
  return parsedSku
    .map((item) => {
      return item.v;
    })
    .join('，');
}

/**
 * 二位数补齐
 */
export function padZero(num) {
  return (num < 10 ? '0' : '') + num;
}

export const isSameObject = (a, b) => JSON.stringify(a) === JSON.stringify(b);
export const cloudData = {
  getShopList: ({ shopList = [] }): ShopList[] => {
    return (shopList || []).map((shop) => {
      const goodsGroupList = (shop.goodsGroupList || []).map((goodsGroup) => {
        const goodsList = (goodsGroup.goodsList || []).map((goods) => {
          const result = pick(goods, [
            'cartId',
            'kdtId',
            'goodsId',
            'skuId',
            'sku',
            'checked',
            'activityId',
            'storeId',
            'channelId',
            'canyinId',
            'alias',
            'title',
            'attachmentUrl',
            'num',
            'payPrice',
            'originPrice',
            'stock',
            'limitNum',
            'activityType',
            'activityAlias',
            'messages',
            'unique',
            'revive',
            'weight',
            'imgUrl',
          ]);

          /* #ifdef web */
          result.stockNum = result.stock;
          result.thumbUrl = image.toWebp(cdnImage(goods.attachmentUrl, '!200x200.jpg'));
          /* #endif */

          return result;
        });

        return {
          goodsList,
          groupActivityInfo: goodsGroup.groupActivityInfo,
        };
      });

      return {
        goodsGroupList,
        kdtId: shop.kdtId,
        shopName: shop.shopName,
      };
    });
  },
  getGoodsList({ shopList, submitData }): GoodsList[] {
    return (shopList || []).map((shop) => {
      const items = [];

      (shop.goodsGroupList || []).forEach((goodsGroup) => {
        (goodsGroup.goodsList || []).forEach((goods) => {
          const item = pick(goods, [
            'cartId',
            'kdtId',
            'goodsId',
            'skuId',
            'propertyIds',
            'properties',
            'sku',
            'checked',
            'activityId',
            'storeId',
            'channelId',
            'canyinId',
            'alias',
            'title',
            'attachmentUrl',
            'num',
            'payPrice',
            'originPrice',
            'stock',
            'limitNum',
            'activityType',
            'activityAlias',
            'messages',
            'unique',
            'revive',
            'weight',
            'imgUrl',
          ]);

          /* #ifdef web */
          item.stockNum = item.stock;
          item.thumbUrl = image.toWebp(cdnImage(goods.attachmentUrl, '!200x200.jpg'));
          /* #endif */

          items.push({ ...item, ...mapKeysToSnakeCase(item) });
        });
      });

      return {
        activities: submitData.activities || [],
        items,
        kdtId: shop.kdtId,
        shopName: shop.shopName,
      };
    });
  },
};
