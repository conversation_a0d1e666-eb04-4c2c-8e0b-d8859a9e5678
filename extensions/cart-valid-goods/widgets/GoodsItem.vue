<template>
  <view class="goods-item custom-class" :id="'cart-id-' + goods.cartId" :style="themeStyle">
    <van-swipe-cell
      :right-width="64"
      async-close
      @close="handleItemDelete"
      :disabled="swipeDisabled"
    >
      {{ goods.skeleton ? 'skeleton' : '' }}
      <view class="goods-item-container">
        <view v-if="isCanChoose" class="checkbox-wrap">
          <van-icon
            size="20px"
            :color="iconColor"
            :name="iconName"
            :custom-style="iconStyle"
            @click.stop="handleCheckboxTap"
          />
        </view>

        <goods
          :goods="goods"
          :edit-mode="editMode"
          :theme-colors="themeColors"
          :theme-style="themeStyle"
          :theme-tag="themeTag"
          :show-estimated-price="!editMode && showEstimatedPrice"
          :logger="logger"
          @item-num-change="handleItemNumChange"
          @item-num-change-logger="handleItemNumChangeLogger"
          @change-goods-sku="handleChangeGoodsSku"
          @goods-img-click="goGoodsDetail"
          @open-combo-popup="$emit('open-combo-popup')"
          @change-swipe-status="changeSwipeStatus"
          @hide-count-down="$emit('hide-count-down')"
          @goods-img-load="$emit('goods-img-load')"
        />
      </view>

      <view slot="right" :class="deleteBtnClassName">删除</view>
    </van-swipe-cell>
  </view>
</template>

<script>
import Toast from '@youzan/vant-tee/dist/toast/toast';
import VanDialog from '@youzan/vant-tee/dist/dialog/index.vue';
import Dialog from '@youzan/vant-tee/dist/dialog/dialog';
import Icon from '@youzan/vant-tee/dist/icon/index.vue';
import SwipeCell from '@youzan/vant-tee/dist/swipe-cell/index.vue';
// import Goods from '../components/Goods.vue';
import { isGoodsCheckboxEnable } from '../utils';
import debounce from '@youzan/weapp-utils/lib/debounce';
import { MAX_GOODS_COUNT } from '../constants';
// 计算属性
function formatComputedData(keys) {
  const checkboxEnabled = isGoodsCheckboxEnable(this.goods, this.editMode);
  const computedMaps = {
    checkboxEnabled() {
      return checkboxEnabled;
    },
    iconStyle() {
      return checkboxEnabled
        ? ''
        : 'background-color: #f7f8fa; border-radius: 18px; color: #c8c9cc;';
    },
    iconName() {
      if (this.isChoose && checkboxEnabled) {
        return 'checked';
      }

      return 'circle';
    },
    iconColor() {
      if (this.isChoose && checkboxEnabled) {
        return this.themeGeneralColor;
      }
      return '#969799';
    },
  };
  const computedKeys = keys && keys.length ? keys : Object.keys(computedMaps);
  const result = computedKeys.reduce((obj, key) => {
    obj[key] = computedMaps[key].call(this);
    return obj;
  }, {});
  return result;
}

export default {
  externalClasses: ['custom-class'],

  components: {
    'van-icon': Icon,
    'van-swipe-cell': SwipeCell,
    'van-dialog': VanDialog,
    // goods: Goods,
  },

  props: {
    goods: {
      type: Object,
    },
    editMode: {
      type: Boolean,
    },
    isChoose: {
      type: Boolean,
    },
    // 是否可以选中
    isCanChoose: {
      type: Boolean,
      default: true,
    },
    // 是否是活动商品
    isActivity: {
      type: Boolean,
      default: false,
    },
    index: {
      type: Number,
      default: 0,
    },
    total: {
      type: Number,
      default: 0,
    },
    themeGeneralColor: String,
    themeGeneralAlpha10Color: String,
    themeColors: {
      type: Object,
      default: () => ({}),
    },
    themeTag: {
      type: Object,
      default: () => ({}),
    },
    showEstimatedPrice: {
      type: Boolean,
      default: false,
    },
    logger: {
      type: Object,
      default: () => ({}),
    },
    themeStyle: {
      type: String,
      default: '',
    },
    goodsCount: {
      type: Number,
      default: 0,
    },
  },

  data() {
    const computedData = formatComputedData.call(this);
    return {
      swipeDisabled: false,
      ...computedData,
    };
  },

  computed: {
    deleteBtnClassName() {
      const classname = ['delete-btn'];
      if (this.index === 0 && !this.isActivity) {
        classname.push('border-top-right');
      }
      if (this.index === this.total - 1) {
        classname.push('border-bottom-right');
      }

      return classname.join(' ');
    },
  },

  watch: {
    goods() {
      this.updateComputedData(['checkboxEnabled', 'iconStyle', 'iconName', 'iconColor']);
    },
    editMode() {
      this.updateComputedData(['checkboxEnabled', 'iconStyle', 'iconName', 'iconColor']);
    },
    isChoose() {
      this.updateComputedData(['iconName', 'iconColor']);
    },
    themeGeneralColor() {
      this.updateComputedData(['iconColor']);
    },
  },
  created() {
    // 初始化防抖函数，将事件触发逻辑包装成防抖函数
    this.debouncedItemChecked = debounce(this.emitItemCheckedEvent.bind(this), 300);
  },

  methods: {
    emitItemCheckedEvent(payload) {
      this.$emit('item-checked', payload);
    },
    updateComputedData(keys) {
      const computedData = formatComputedData.call(this, keys);
      Object.keys(computedData).forEach((key) => {
        this[key] = computedData[key];
      });
    },

    changeSwipeStatus(status) {
      this.swipeDisabled = status;
    },
    handlePreCheck() {
      // 商品大于一定数量个时，增加isUpdatingCartGoodsList 锁，防止多次触发接口导致数据混乱。
      if (this.goodsCount > MAX_GOODS_COUNT && this.ctx.data.isUpdatingCartGoodsList) {
        Toast('正在更新购物车，请稍后重试');
        return false;
      }
      this.ctx.data.isUpdatingCartGoodsList = true;
      return true;
    },
    handleCheckboxTap(isVerifyCheckboxEnabled = true) {
      if (!this.handlePreCheck()) {
        return;
      }
      if (!this.isCanChoose || (isVerifyCheckboxEnabled && !this.checkboxEnabled)) {
        // 不能低于起售
        if (this.goods.startSaleNum && this.goods.num < this.goods.startSaleNum) {
          return Toast(`该商品${this.goods.startSaleNum}件起售哦`);
        }

        // 待开售商品不能选中
        if (this.goods.isNotStartSold) {
          return Toast(this.goods.disableSelectMsg);
        }
        return;
      }
      // 根据当前选中状态进行反选，如果选中请求出错会再次更新。
      this.iconName = this.isChoose ? 'circle' : 'checked';
      this.iconColor = this.isChoose ? '#969799' : this.themeGeneralColor;

      // 使用防抖函数触发事件
      const startTime = Date.now();
      this.debouncedItemChecked({
        rangeType: 'single',
        type: this.isChoose ? 'remove' : 'add',
        goods: this.goods,
        isActivity: this.isActivity,
        clickStartTime: startTime,
      });
    },

    handleItemNumChange(detail) {
      const startTime = Date.now();
      this.$emit('item-num-change', {
        ...detail,
        goods: this.goods,
        isActivity: this.isActivity,
        clickStartTime: startTime,
      });
    },

    handleItemNumChangeLogger(param) {
      this.$emit('item-num-change-logger', param);
    },

    handleChangeGoodsSku() {
      this.$emit('change-goods-sku', this.goods);
    },

    handleItemDelete(detail) {
      const { instance, position } = detail;
      if (position === 'cell') return instance.close();
      const eventData = {
        goods: this.goods,
        isActivity: this.isActivity,
      };
      if (this.editMode) {
        const isCourse = this.goods.goodsType === 31;
        Dialog.confirm({
          message: `确定要删除这个${isCourse ? '课程' : ''}么?`,
        })
          .then(() => {
            this.$emit('item-delete', eventData);
            instance.close();
          })
          .catch(() => {
            instance.close();
          });
      } else {
        this.$emit('item-delete', eventData);
        instance.close();
      }
    },

    goGoodsDetail({ alias }) {
      let url = `/pages/goods/detail/index?alias=${alias}`;
      /* #ifdef web */
      url = `/wscgoods/detail/${alias}`;
      const {
        isKsApp = false,
        isXhsApp = false,
        isAlipayApp = false,
        isQQApp = false,
      } = window._global?.miniprogram || {};
      if (isKsApp || isXhsApp || isAlipayApp || isQQApp) {
        url = `/packages/goods-v3/detail/index?alias=${alias}`;
      }
      /* #endif */
      this.$emit('to-goods-detail', url);
    },
  },
};
</script>

<style lang="scss" scoped>
.goods-item {
  background: #fff;
  overflow: hidden;

  &-container {
    padding: var(--theme-page-card-padding-top, 12px) var(--theme-page-card-padding-right, 12px)
      var(--theme-page-card-padding-bottom, 12px) var(--theme-page-card-padding-left, 12px);
  }
}

.checkbox-wrap {
  float: left;
  height: 105px;
  width: 20px;
  text-align: center;
  line-height: 100px;
  color: #38f;
  display: flex;
  align-items: center;
}

.delete-btn {
  background: #f44;
  color: #fff;
  font-weight: bold;
  display: flex;
  justify-content: center;
  align-items: center;
  font-size: 14px;
  width: 64px;
  height: 100%;
}

.delete-btn.border-top-right {
  border-top-right-radius: var(--theme-radius-card, 8px);
}

.delete-btn.border-bottom-right {
  border-bottom-right-radius: var(--theme-radius-card, 8px);
}
</style>
