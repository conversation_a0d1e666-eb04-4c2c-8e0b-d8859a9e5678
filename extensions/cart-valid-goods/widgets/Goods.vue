<template>
  <view class="goods-item__inner" @click.stop="handleGoodsImgTap" :style="themeStyle">
    <view class="goods-img">
      <image
        class="goods-img__image"
        :mode="goods.imgMode"
        :src="goods.imgUrl"
        @load="$emit('goods-img-load')"
      />
    </view>

    <view class="goods-item--right">
      <view
        v-if="!editMode"
        :class="[goods.title ? 'goods-title' : 'goods-title-not-exist', 't-multi-ellipsis--l2']"
      >
        <image
          v-if="goods.settlementRule && goods.goodsTitleTag"
          :class="['goods-title-tag', goods.goodsSettlementMark]"
          lazy-load="true"
          :src="goods.goodsTitleTag"
        />
        <text class="goods-title-text">{{ goods.title }}</text>
      </view>

      <view v-else :class="['goods-title', 't-multi-ellipsis--l2']">
        <text class="goods-title-text">{{ goods.title }}</text>
      </view>

      <!-- 非待复活商品 -->
      <block v-if="!goods.revive">
        <view v-if="goods.sku" :class="['goods-sku', !!goods.tariffPriceText ? mb16 : '']">
          <view
            :class="['goods-sku-container', !isCanChangeSku ? 'goods-sku-container-normal' : '']"
            @click.stop="handleChangeGoodsSku"
          >
            <text class="tee-text">{{ goods.sku }}</text>
            <van-icon v-if="isCanChangeSku" name="arrow-down" custom-class="goods-sku-arrow" />
          </view>
        </view>

        <view class="goods-into-bottom">
          <!-- 商品图片标 -->
          <view class="cart-goods__secured-container">
            <image
              v-if="yzGuarantee && goods.hideGuarantee === false"
              :src="GOODS_IMG_TAG_MAP.SECURED"
              alt="有赞放心购"
              class="cart-goods__secured-container-protect"
            />
            <view
              v-if="
                yzGuarantee && goods.hideGuarantee === false && goods.isSevenDayUnconditionalReturn
              "
              class="seven-return-dot"
              >·</view
            >
            <view v-if="goods.isSevenDayUnconditionalReturn" class="seven-return"
              >7天无理由退货</view
            >
          </view>

          <view v-if="goodsTagList.length && !goods.isEduIosOnlineGoods" class="goods-tags">
            <!-- FIXME: for item props -->
            <van-tag
              v-for="(item, index) in goodsTagList"
              :key="index"
              :plain="themeTag.plain"
              :hairline="themeTag.plain"
              custom-class="goods-tags__tag"
              >{{ item }}</van-tag
            >
          </view>

          <view
            v-if="
              !goods.isEduIosOnlineGoods &&
              (goods.stockLimitText || goods.startSaleNumAndLimitDesc || goods.cutPriceDesc)
            "
            class="cart-goods__message-tips"
          >
            <view v-if="!!goods.stockLimitText" class="cart-goods__message-tips-box">{{
              goods.stockLimitText
            }}</view>
            <view v-if="goods.startSaleNumAndLimitDesc" class="cart-goods__message-tips-box">
              <view v-if="!!goods.stockLimitText" class="cart-goods__message-tips-divider"></view>
              <text>{{ goods.startSaleNumAndLimitDesc }}</text>
            </view>
            <view v-if="!!goods.cutPriceDesc" class="cart-goods__message-tips-box">
              <text>{{ goods.cutPriceDesc }}</text>
            </view>
          </view>

          <view class="cart-goods__goods-message">
            <sale-count-down
              v-if="goods.isNotStartSold"
              :start-sold-time="goods.startSoldTime"
              @hide-countdown="$emit('hide-count-down')"
            />

            <!-- 预售商品发货时间 -->
            <view v-if="goods.presaleDate" class="presale-date">
              {{ goods.presaleDate }}
              <text v-if="deliveryTimeStr"> | {{ deliveryTimeStr }} </text>
            </view>
            <view v-else-if="deliveryTimeStr" class="delivery-time">
              {{ deliveryTimeStr }}
            </view>

            <view class="goods-tax" v-if="!!goods.tariffPriceText && !goods.isEduIosOnlineGoods">
              <text>进口税：{{ goods.tariffPriceText }}</text>
            </view>
          </view>

          <view
            v-if="goods.price !== null && goods.price >= 0 && !goods.isEduIosOnlineGoods"
            class="goods-bottom"
          >
            <view :class="'goods-price'">
              <price
                :price="goods.price"
                :origin-price="showEstimatedPrice && formatEstimatedPrice ? '' : goods.originPrice"
                :points-price="goods.pointsPrice"
                :points-name="shopCart.pointsName"
                :origin-style="'display: block;margin-left: 0;margin-top: 4px;'"
                :need-flex="false"
              />
            </view>

            <view class="goods-num">
              <stepper
                v-if="goods.isCanStepperFormater"
                custom-class="stepper"
                :value="goodsNum"
                integer
                min="1"
                step="1"
                :max="goods.goodsLimitNum"
                @click.stop="noopFn"
                :long-press="false"
                :disable-minus="stepperDisableMinus"
                :disable-plus="stepperDisablePlus"
                @plus="handleStepPlus"
                @minus="handleStepMinus"
                @change="handleNumStepChange"
                @overlimit="handleStepOverLimit"
                @click.native.stop=""
              />

              <view v-else>x{{ goods.num }}</view>
            </view>
          </view>
          <view v-if="showEstimatedPrice && formatEstimatedPrice" class="estimated-price">
            每件预计到手¥{{ formatEstimatedPrice }}
          </view>

          <view v-if="goods.isEduIosOnlineGoods" class="edu-err-msg">iOS端小程序不支持在线课</view>

          <view v-else-if="goods.errorMsg" class="err-msg">{{ goods.errorMsg }}</view>
        </view>
      </block>
      <!-- 待复活商品 -->
      <block v-else>
        <view class="goods-revive" :style="priceStyle">
          <text>请重新选择商品规格</text>

          <view class="goods-revive__tag" @click.stop="handleChangeGoodsSku"> 重选 </view>
        </view>
      </block>
    </view>
    <!-- 组合商品展示 -->
    <combo-detail
      v-if="goods.comboDetailFormat && goods.comboDetailFormat.length"
      :combo-detail="goods.comboDetailFormat"
      @open-combo-popup="$emit('open-combo-popup')"
      @change-swipe-status="changeSwipeStatus"
    />
    <van-toast ref="van-toast" />
  </view>
</template>

<script>
/* eslint-disable @youzan/domain/forbid-hardcode-domain-name */
import Tag from '@youzan/vant-tee/dist/tag/index.vue';
import Icon from '@youzan/vant-tee/dist/icon/index.vue';
import VanToast from '@youzan/vant-tee/dist/toast/index.vue';
import Toast from '@youzan/vant-tee/dist/toast/toast';
import SaleCountDown from '../components/SaleCountDown.vue';
import ComboDetail from '../components/ComboDetail.vue';
import money from '@youzan/weapp-utils/lib/money';
import Stepper from '@youzan/wsc-tee-trade-common/components/Stepper.vue';
import get from '@youzan/utils/object/get';
import { errorToast, cdnImage } from '@youzan/tee-biz-util';
import { mapState } from '@ranta/store';
import { mapData } from '@youzan/ranta-helper';
import debounce from '@youzan/weapp-utils/lib/debounce';

const GOODS_IMG_TAG_MAP = {
  // 有赞放心购
  SECURED: cdnImage('icon/guarantee-icon.svg'),
};

const DRUG_QUALITY_LIMIT = 10000;

// 为什么这样写？计算属性带来的渲染副作用极大，为了提升渲染速度做此修改
// 移除computed，改为data计算与watch重计算
function formatComputedData(keys) {
  if (keys && typeof keys === 'string') {
    keys = [keys];
  }
  const computedMaps = {
    // 依赖组件其他属性的计算属性
    goodsTagList() {
      const { activityTag, isInstallment, bizExtension = {}, isMemberDiscount } = this.goods;

      const goodsTagList = [];

      if (get(bizExtension, 'cartBizMark.PRE_SALE_TYPE') === '0') {
        goodsTagList.push('预售');
      }

      isMemberDiscount && goodsTagList.push(`${get(this.memberConfig, 'name') || '会员'}价`);

      /* #ifdef web */
      if (isInstallment && !this.isAlipayApp && !this.isQQApp) {
        goodsTagList.push('分期支付');
      }
      /* #endif */

      if (activityTag && activityTag !== '会员折扣') {
        goodsTagList.push(activityTag);
      }

      return goodsTagList;
    },

    presaleDateStyle() {
      return `color: ${this.themeColors.notice}`;
    },

    goodsNum() {
      return this.goods.num || 1;
    },

    stepperDisablePlus() {
      const { maxNum, num } = this.goods;
      return !!maxNum && num >= maxNum;
    },

    stepperDisableMinus() {
      return (
        !!this.goods.startSaleNum &&
        this.goods.num <= this.goods.startSaleNum &&
        !this.goods.limitNum
      );
    },

    isCanChangeSku() {
      const isNotMeetStartSaleNum =
        !!this.goods.startSaleNum && this.goods.num < this.goods.startSaleNum;
      const isPlusBuyGoods = +this.goods.activityType === 24;
      // 判断是否套餐
      const isCombo = !!this.goods.comboDetail;

      return !isCombo && !isPlusBuyGoods && !isNotMeetStartSaleNum && !this.goods.canyinId;
    },
    formatEstimatedPrice() {
      if (!this.showEstimatedPrice) {
        return '';
      }
      const val = this.estimatedPrice;
      if (!val) return '';

      return money(val).toYuan();
    },
  };
  const computedKeys = keys && keys.length ? keys : Object.keys(computedMaps);

  return computedKeys.reduce((obj, key) => {
    obj[key] = computedMaps[key].call(this);
    return obj;
  }, {});
}

export default {
  components: {
    stepper: Stepper,
    'van-icon': Icon,
    'van-tag': Tag,
    'van-toast': VanToast,
    'sale-count-down': SaleCountDown,
    'combo-detail': ComboDetail,
    // TODO: view
  },

  props: {
    goods: Object,
    editMode: Boolean,
    themeColors: {
      type: Object,
      default: () => ({}),
    },
    themeTag: {
      type: Object,
      default: () => ({}),
    },
    showEstimatedPrice: {
      type: Boolean,
      default: false,
    },
    themeStyle: {
      type: String,
      default: '',
    },
  },

  data() {
    const initedData = {};
    /* #ifdef web */
    initedData.isAlipayApp = get(window, '_global.miniprogram.isAlipayApp', false);
    initedData.isQQApp = get(window, '_global.miniprogram.isQQApp', false);
    /* #endif */
    const computedData = formatComputedData.call(this);
    return {
      GOODS_IMG_TAG_MAP,
      priceStyle: '',
      isShowCutPriceDescDivider: true,
      memberConfig: {},
      // 异步数据
      deliveryTimeStr: '',
      yzGuarantee: false,
      estimatedPrice: '',
      ...computedData,
      ...initedData,
      ...mapState(this, ['numChangeInvalid', 'shopCart']),
    };
  },
  watch: {
    goods() {
      this.updateComputedData();
    },
    memberConfig() {
      this.updateComputedData(['goodsTagList']);
    },
    themeColors() {
      this.updateComputedData(['presaleDateStyle']);
    },
    estimatedPrice() {
      this.updateComputedData(['formatEstimatedPrice']);
    },
  },
  created() {
    mapData(this, ['memberConfig']);
    this.handleStepChangeDebounce = debounce(this.handleStepChange.bind(this), 500);
    mapData(this, ['cartAsyncData'], {
      isSetData: false,
      callback() {
        this.updateAsyncData(this.ctx.data.cartAsyncData);
      },
    });
  },
  mounted() {
    this.viewLog();
    this.computedPriceStyle();
    this.computedMessageDivider();
  },
  methods: {
    updateComputedData(keys) {
      const computedData = formatComputedData.call(this, keys);
      Object.keys(computedData).forEach((key) => {
        this[key] = computedData[key];
      });
    },

    // 根据cartAsyncData更新异步数据
    updateAsyncData(cartAsyncData) {
      const {
        securedItems = [],
        itemDispatchInfoList = [],
        estimatedPriceList = [],
      } = cartAsyncData;
      const { goodsId, skuId, cartId, alias } = this.goods;

      // 更新有赞担保数据
      if (securedItems.length) {
        const itemData = securedItems.find((item) => item.alias === alias) || {};
        this.yzGuarantee = itemData.yzSecured || false;
      }

      // 更新预计送达时间数据
      if (itemDispatchInfoList.length) {
        const itemDispatch =
          itemDispatchInfoList.find((item) => item.uniqueKey === String(cartId)) || {};
        this.deliveryTimeStr = itemDispatch.estimateDeliveryTimeDesc || '';
      }

      // 更新预估到手价数据
      if (estimatedPriceList.length) {
        const priceData = estimatedPriceList.find(
          (item) => item.goodsId === goodsId && item.skuId === skuId
        );
        if (priceData) {
          this.estimatedPrice = priceData.estimatedPrice;
        }
      }
    },
    changeSwipeStatus(status) {
      this.$emit('change-swipe-status', status);
    },
    handleStepPlus() {
      this.handleStepPlusorMinusLogger(true);
    },
    handleStepMinus() {
      this.handleStepPlusorMinusLogger(false);
    },

    handleStepPlusorMinusLogger(isPlus) {
      const { sku, skuData, skuId, goodsId, title } = this.goods;

      const param = {
        et: 'click',
        ei: `cart_${isPlus ? 'increase' : 'decrease'}_goods_num`,
        en: `购物车页面-${isPlus ? '增加' : '减少'}商品数量`,
        params: {
          no_sku: sku ? 0 : 1,
          sku_id: skuId,
          sku_name: skuData,
          goods_id: goodsId,
          goods_name: title,
        },
      };

      this.$emit('item-num-change-logger', param);
    },
    handleNumStepChange(e) {
      this.handleStepChangeDebounce(e);
    },
    handleStepChange(e) {
      // if (e > this.goodsMaxNum) {
      //   Toast("就这么几件了");
      // }

      const eventData = {
        cartId: this.goods.cartId,
        num: +e,
      };

      this.$emit('item-num-change', eventData);
    },

    handleGoodsImgTap() {
      if (this.editMode) return;
      const { alias } = this.goods;
      if (alias) {
        this.$emit('goods-img-click', { alias });
      } else {
        errorToast({ goods: this.goods }, { message: '商品数据异常，请刷新重试', log: true });
      }
    },

    handleStepOverLimit(detail) {
      if (this.numChangeInvalid) {
        return;
      }
      const isCourse = this.goods.goodsType === 31;
      if (this.stepperDisableMinus) {
        Toast(`该商品${this.goods.startSaleNum}件起售哦`);
      } else if (detail === 'minus') {
        Toast(isCourse ? '最少购买1门课' : '最少购买1件哦');
      } else if (
        // 当前商品为处方药时，超过处方药限制购买数，toast提示
        get(this.goods, 'bizExtension.cartBizMark.IS_PRESCRIPTION_DRUG') === '1' &&
        this.goods.num >= DRUG_QUALITY_LIMIT &&
        detail === 'plus'
      ) {
        Toast(`为保障用药安全，不能添加更多`);
      } else if (!this.goods.limitNum || this.goods.limitNum > this.goods.stockNum) {
        Toast(isCourse ? '超出该课程可购买数量' : '该商品不能购买更多哦');
      } else if (this.goods.quotaCycle === 4) {
        // 限购类型为按单限购时，提示信息与其他限购类型不同
        Toast(`该商品每单限购${this.goods.limitNum}件`);
      } else {
        Toast(`商品限购${this.goods.limitNum}件`);
      }
    },

    handleChangeGoodsSku() {
      if (this.isCanChangeSku || this.goods.revive) {
        this.$emit('change-goods-sku');
      }
    },

    computedPriceStyle() {
      this.priceStyle = '';
      const query = this.createSelectorQuery();
      const goodsRight = query.select('.goods-item--right');
      goodsRight
        .boundingClientRect((res) => {
          if (!res) return;
          const { height } = res;
          if (height < 96) {
            this.priceStyle = `padding-top: ${96 - height}px`;
          }
        })
        .exec();
    },

    computedMessageDivider() {
      const query = this.createSelectorQuery();
      const goodsRight = query.select('.cart-goods__message-tips');
      goodsRight
        .boundingClientRect((res) => {
          if (!res) return;
          const { height } = res;
          if (height > 24) {
            this.isShowCutPriceDescDivider = false;
          }
        })
        .exec();
    },

    viewLog() {
      if (+this.goods.activityType === 1) {
        this.logger &&
          this.logger.log({
            et: 'view', // 事件类型
            ei: 'cart_limited_discount_view', // 事件标识
            en: ' 购物车限时折扣曝光', // 事件名称
            params: {
              item_type: 'limitdiscount',
              activity_type: 'limitdiscount',
              kdt_id: this.goods.kdtId,
            }, // 事件参数
          });
      }
    },
    noopFn() {},
  },
};
</script>

<style lang="scss">
.goods-item__inner {
  min-height: 96px;
  margin-left: 37px;

  &_invalid {
    margin-left: 15px;
  }
}

.goods-img {
  float: left;
  height: 96px;
  width: 96px;
  background-size: cover;
  border-radius: 8px;
  overflow: hidden;

  &__image {
    max-height: 100%;
    max-width: 100%;
    width: 96px;
    height: 96px;
    background-color: #fff;
    border-radius: 8px;
  }
}

.goods-tags {
  margin-bottom: 8px;
  margin-top: 0;
  font-size: 0;

  &__tag {
    --tag-font-size: var(--eo-font-size-12, 12px);
    --tag-line-height: 1.3;

    padding: 1px 4px;
    margin-right: 8px;
    vertical-align: middle;
    color: var(--theme-tag-ump-color, --ump-tag-text) !important;
    background: var(--theme-tag-ump-bg-color, --ump-tag-bg) !important;
    border-radius: 2px;

    &::after {
      border-radius: var(--theme-radius-tag, 0) !important;
      border-color: var(--theme-tag-ump-border-color, --main-bg) !important;
    }
  }
}

.goods-title {
  margin-bottom: 8px;
  display: flex;
  align-items: center;
}

.goods-title-not-exist {
  margin-bottom: 0;
}

.goods-title-text {
  font-size: 16px;
  line-height: 22px;
  color: #323233;
  font-weight: 500;
  vertical-align: middle;
  display: -webkit-box;
  overflow: hidden;
  text-overflow: ellipsis;
  -webkit-line-clamp: 2;
  -webkit-box-orient: vertical;
}

.goods-title-tag {
  margin-right: 2px;
  width: calc(43px * var(--font-size-scale, 1));
  height: calc(16px * var(--font-size-scale, 1));
  display: inline-block;
  vertical-align: middle;
  flex: none;

  &.HAITAO {
    width: calc(28px * var(--font-size-scale, 1));
  }

  &.PERIOD_BUY,
  &.IS_DRUG_GOODS {
    width: calc(38px * var(--font-size-scale, 1));
  }
}

.goods-sku {
  margin-bottom: 8px;
  line-height: 12px;

  &.mb16 {
    margin-bottom: 16px;
  }
}

.goods-sku-container {
  display: inline-flex;
  align-items: center;
  justify-content: flex-start;
  padding: 3px 8px;
  background: #f7f8fa;
  border-radius: var(--theme-radius-tag, 4px);
  font-size: 0;
  color: #969799;

  .tee-text {
    font-size: var(--eo-font-size-14, 14px);
    margin-right: 10px;
    line-height: 1.3;
  }

  .goods-sku-arrow {
    font-size: var(--eo-font-size-12, 12px);
    height: 12px;
    line-height: 12px;
    vertical-align: middle;
  }
}

.goods-sku-container-normal {
  padding: 0;
  background: transparent;

  text {
    margin: 0;
  }
}

.goods-item--right {
  position: relative;
  margin-left: 104px;
  min-height: 96px;
}

.goods-bottom {
  display: flex;
  justify-content: space-between;
  align-items: center;
  width: 100%;
  margin-top: var(--theme-trade-sm-gutter, 10px);
}

.origin-block {
  display: block;
  margin-left: 0;
  margin-top: 4px;
}

.goods-price {
  display: inline-block;
  font-size: 14px;
  font-weight: var(--theme-common-price-font-weight, 600);
  font-family: Avenir;
  color: var(--price, #323233);
}

.pt6 {
  padding-top: 4px;
}

.goods-num {
  color: #666;
  font-size: var(--eo-font-size-12, 12px);
}

.activity-tag {
  background: #f44;
  border-bottom-right-radius: var(--theme-radius-card, 8px);
  border-top-right-radius: 8px;
  bottom: 10%;
  color: #fff;
  display: inline-block;
  font-size: 10px;
  padding: 0 4px;
  position: absolute;
  z-index: 1;
}

.presale-date,
.delivery-time,
.goods-tax {
  font-size: 14px;
  color: #323233;
  line-height: 1.3;
  margin-bottom: 6px;
}

.stock-less,
.start-sale-num {
  font-size: 12px;
  line-height: 16px;
  color: var(--ump-icon, #323233);
}

.err-msg {
  font-size: 12px;
  color: #323233;
  line-height: 16px;
}

.edu-err-msg {
  font-size: 12px;
  color: #323233;
  line-height: 16px;
  margin-bottom: 12px;
}

.cart-goods {
  &__img-tags {
    margin-bottom: 8px;
    padding-top: 8px;
  }

  &__secured {
    &-container {
      display: flex;
      align-items: center;
      margin-top: 4px;

      &-protect {
        height: calc(15px * var(--font-size-scale, 1));
        width: calc(58px * var(--font-size-scale, 1));
        margin-bottom: 10px;
      }

      .seven-return {
        line-height: 14px;
        font-size: 14px;
        color: #00b355;
        border-radius: 2px;
        margin-bottom: 10px;
        /* #ifdef weapp */
        padding-top: 2px;
        /* #endif */
      }

      .seven-return-dot {
        margin: 0 4px;
        margin-bottom: 10px;
        color: #00b355;
      }
    }
  }

  &__message-tips {
    display: flex;
    align-items: center;
    margin-top: 4px;
    flex-wrap: wrap;

    &-divider {
      margin: 0 8px;
      color: #dcdee0;
      width: 0;
      height: 12px;
      border-right: 1px solid #ebedf0;
    }

    &-box {
      display: flex;
      align-items: center;
      margin-bottom: 8px;
      font-size: 14px;
      line-height: 1.3;
      color: var(--general, '#EE0A24');
    }
  }

  &__goods-message {
    display: flex;
    flex-direction: column;
    color: #323233;
  }
}

.custom-stepper {
  &__input,
  &__plus,
  &__minus {
    margin-left: var(--theme-page-stepper-input-margin-left, 1px) !important;
    margin-right: var(--theme-page-stepper-input-margin-right, 1px) !important;
    background-color: var(--theme-page-stepper-background-color, #f2f3f5) !important;
  }

  &__input {
    border: 1px solid var(--theme-page-stepper-border-color, rgba(0, 0, 0, 0)) !important;
  }

  &__plus,
  &__minus {
    border-color: var(--theme-page-stepper-border-color, rgba(0, 0, 0, 0)) !important;
    color: #323233;
    border-top: 1px solid;
    border-bottom: 1px solid;

    &--disabled {
      color: var(--theme-page-stepper-color-disabled, #c8c9cc) !important;
    }
  }

  &__plus {
    border-top-right-radius: var(--theme-page-stepper-border-top-right-radius, 4px) !important;
    border-bottom-right-radius: var(
      --theme-page-stepper-border-bottom-right-radius,
      4px
    ) !important;
    border-right: 1px solid;
    border-left: 0;
  }

  &__minus {
    border-top-left-radius: var(--theme-page-stepper-border-top-left-radius, 4px) !important;
    border-bottom-left-radius: var(--theme-page-stepper-border-bottom-left-radius, 4px) !important;
    border-left: 1px solid;
    border-right: 0;
  }
}

.goods-cut-price {
  font-size: 12px;
  line-height: 16px;
  color: var(--ump-icon, #323233);
  letter-spacing: 0;
}

.goods-revive {
  font-size: var(--eo-font-size-12, 12px);
  color: #323233;
  display: flex;
  align-items: center;
  justify-content: space-between;

  &__tag {
    width: calc(40px * var(--font-size-scale, 1));
    height: var(--eo-font-size-20, 20px);
    line-height: var(--eo-font-size-20, 20px);
    padding: 0;
    text-align: center;
    display: block;
    background: #fff;
    color: var(--main-bg);
    border: 1px solid var(--main-bg);
    border-radius: calc(var(--theme-radius-button, 10px) * var(--font-size-scale, 1)) !important;
  }
}

.price--currency {
  font-size: 12px;
}

.price--integer {
  font-size: 18px;
  font-weight: 600;
}

.price--decimal {
  font-size: 12px;
}

.price--estimated {
  font-size: 14px;
  color: var(--theme-color);
}

.price--origin {
  font-size: 14px;
  color: #323233;
  text-decoration: line-through;
}

.estimated-price {
  font-size: 14px;
  line-height: 16px;
  color: var(--general, '#EE0A24');
  margin-top: 8px;
}
</style>
