<template>
  <view>
    <custom-list-header v-if="isShowCustomGoodsListHeader" />

    <goods-group-ump
      v-if="currentGroup.groupActivityInfo && !isShowCustomGoodsListHeader"
      :kdt-id="kdtId"
      :offline-id="offlineId"
      :is-editing="isEditing"
      :goods-list="currentGroup.goodsList"
      :checked-goods-list="checkedGoodsList"
      :activity-info="currentGroup.groupActivityInfo"
      :theme-general-color="themeGeneralColor"
      :theme-general-alpha-10-color="themeGeneralAlpha10Color"
      :theme-css="themeCSS"
      :theme-colors="themeColors"
      :logger="logger"
      @change-item-checked="
        handleItemChecked(
          goodsIndex,
          goodsGroupIndex,
          /* #ifdef web */ { detail: $event } /* #endif */
        )
      "
      @refresh-cart-goods-list="$emit('refresh-cart-goods-list')"
      :cloud-activity-id="
        (currentGroup.groupActivityInfo && currentGroup.groupActivityInfo.activityId) || 0
      "
      :cloud-index="goodsGroupIndex"
    />

    <custom-goods-item v-if="isShowCustomGoodsItem" :goods-group="currentGroup" />

    <view v-else>
      <view v-for="(goods, goodsIndex) in currentGroup.goodsList" :key="goods.goodsId">
        <view>
          <goods-item-ump :key="goodsIndex" :goods-item="goods" />
          <goods-item
            custom-class="goods-group__goods-item"
            :is-choose="!!(isEditing ? editCheckedGoods[goods.cartId + ''] : goods.checked)"
            :goods="goods"
            :edit-mode="isEditing"
            :is-activity="!!currentGroup.groupActivityInfo"
            :is-can-choose="
              !(
                !isEditing &&
                isIOS &&
                goods.goodsType === 31 &&
                goods.bizExtension &&
                goods.bizExtension.cartBizMark &&
                goods.bizExtension.cartBizMark.isOnlineCourse === '1'
              )
            "
            :index="goodsGroupIndex"
            :total="currentGroup.goodsList && currentGroup.goodsList.length"
            :theme-general-color="themeGeneralColor"
            :theme-general-alpha-10-color="themeGeneralAlpha10Color"
            :theme-colors="themeColors"
            :theme-style="themeStyle"
            :theme-tag="themeTag"
            :show-estimated-price="showEstimatedPrice"
            :goods-count="goodsCount"
            :logger="logger"
            @item-checked="
              handleItemChecked(
                goodsIndex,
                goodsGroupIndex,
                /* #ifdef web */ { detail: $event } /* #endif */
              )
            "
            @item-num-change="
              handleItemNumChange(
                goodsIndex,
                goodsGroupIndex,
                /* #ifdef web */ { detail: $event } /* #endif */
              )
            "
            @item-num-change-logger="handleItemNumChangeLogger"
            @item-delete="
              handleItemDelete(
                goodsIndex,
                goodsGroupIndex,
                /* #ifdef web */ { detail: $event } /* #endif */
              )
            "
            @change-goods-sku="handleChangeGoodsSku"
            @open-combo-popup="openComboPopup(goods)"
            @to-goods-detail="toGoodsDetail"
            @hide-count-down="hideCountDown"
            @goods-img-load="onGoodsImgLoad(goodsGroupIndex, goodsIndex)"
            :cloud-cart-id="goods.cartId"
            :cloud-goods-id="goods.goodsId"
            :cloud-index="goodsIndex"
          />
        </view>
      </view>
    </view>
    <!-- 叠加活动赠品 -->
    <view
      v-if="currentGroup.groupActivityInfoList && currentGroup.groupActivityInfoList.length > 0"
    >
      <present
        v-for="(groupActivityInfo, index) in currentGroup.groupActivityInfoList"
        :key="index"
        :activity-id="groupActivityInfo.activityId"
        :group-activity-info="groupActivityInfo"
        :present-list="presentData[groupActivityInfo.activityId]"
        :theme-general-color="themeGeneralColor"
        :can-select-present="canSelectPresent"
        @on-show="handleChangePresentPopup"
        @change-sku="handleChangePresentSku"
      />
    </view>
    <!-- 非叠加活动赠品 -->
    <present
      v-else-if="currentGroup.groupActivityInfo"
      :activity-id="currentGroup.groupActivityInfo.activityId"
      :can-select-present="canSelectPresent"
      :group-activity-info="currentGroup.groupActivityInfo"
      :present-list="
        presentData[currentGroup.groupActivityInfo && currentGroup.groupActivityInfo.activityId]
      "
      :theme-general-color="themeGeneralColor"
      @on-show="handleChangePresentPopup"
      @change-sku="handleChangePresentSku"
    />
    <combo-detail-popup ref="combo-detail-popup" />
  </view>
</template>

<script>
import { mapState, mapActions } from '@ranta/store';
import { mapEvent } from '@youzan/ranta-helper-tee';

import ComboDetailPopup from './ComboDetailPopup.vue';
import get from '@youzan/utils/object/get';

export default {
  name: 'goods-group',
  components: {
    'combo-detail-popup': ComboDetailPopup,
  },
  props: {
    goodsGroup: {
      type: Object,
      default: () => ({}),
    },
    goodsGroupIndex: Number,
    goodsCount: Number,
  },
  data() {
    const { logger } = this.ctx;
    return {
      logger,
      isShowCustomGoodsListHeader: get(this.ctx, 'widgets.CustomGoodsListHeader'),
      isShowCustomGoodsItem: get(this.ctx, 'widgets.CustomGoodsItem'),
      currentGroup: {},
      ...mapState(this, [
        'kdtId',
        'offlineId',
        'editCheckedGoods',
        'checkedGoodsList',
        'themeCSS',
        'themeColors',
        'themeTag',
        'themeGeneralColor',
        'themeGeneralAlpha10Color',
        'canSelectPresent',
        'isIOS',
        'isEditing',
        'presentData',
        'showEstimatedPrice',
        'themeStyle',
      ]),
    };
  },
  watch: {
    goodsGroup: {
      handler(newVal) {
        this.currentGroup = newVal;
      },
      immediate: true,
    },
  },
  mounted() {
    this.initActions();
    mapEvent(this, {
      shopCartUpdated: ({ goodsGroupIndex }) => {
        // 不是当前分组，则return
        if (goodsGroupIndex !== this.goodsGroupIndex) {
          return;
        }
        this.ctx.data.shopCart.goodsGroupList.forEach((goodsGroup, index) => {
          if (index === goodsGroupIndex) {
            // 深拷贝，防止对象相同导致组件不更新
            this.currentGroup = JSON.parse(JSON.stringify(goodsGroup));
          }
        });
      },
    });
  },
  methods: {
    handleItemNumChangeLogger(param) {
      this.ctx.logger && this.ctx.logger.log(param);
    },
    onGoodsImgLoad(groupIndex, goodsIndex) {
      // 第一个商品 第一张图
      if (groupIndex === 0 && goodsIndex === 0) {
        this.ctx.hummer.mark?.log?.({ tag: 'trade-cart', scene: ['route'] });
      }
    },
    openComboPopup(goods) {
      const { comboDetail } = goods;
      this.$refs['combo-detail-popup'].openPopup(comboDetail);
    },
    toGoodsDetail(link) {
      this.ctx.process.invoke('navigateFromCart', { link });
    },
    hideCountDown() {
      this.ctx.event.emit('updateCartGoodsList');
    },
    handleChangePresentPopup(value) {
      this.$emit('on-present-popup-show', value);
    },
    handleChangePresentSku(value) {
      this.$emit('change-present-sku', value);
    },
    handleChangeGoodsSku(value) {
      this.$emit('change-goods-sku', value);
    },
    initActions() {
      mapActions(this, ['handleItemChecked', 'handleItemDelete', 'handleItemNumChange']);
    },
  },
};
</script>
